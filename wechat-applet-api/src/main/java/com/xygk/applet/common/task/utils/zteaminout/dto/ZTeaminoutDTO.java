package com.xygk.applet.common.task.utils.zteaminout.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 迁移班组进退场表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-24
 */
@Data
@ApiModel(value = "迁移班组进退场表")
public class ZTeaminoutDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "项目id")
	private String projectid;
		@ApiModelProperty(value = "班组编号(关联项目班组信息)")
	private String teamsysno;
		@ApiModelProperty(value = "进出场类型(1-进，2-出)")
	private String inout;
		@ApiModelProperty(value = "发生时间")
	private Date occurtime;

}