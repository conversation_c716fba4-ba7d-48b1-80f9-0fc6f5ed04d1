package com.xygk.applet.common.task.utils.zcorp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 迁移参建单位信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("Z_CORP")
public class ZCorpEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 企业名称
     */
	@TableField("corpname")
	private String corpName;
    /**
     * 企业注册地区
     */
	@TableField("areacode")
	private String areacode;
    /**
     * 统一社会信用代码
     */
	@TableField("corpcode")
	private String corpCode;
    /**
     * 注册日期
     */
	@TableField("registerdate")
	private Date registerDate;
    /**
     * 参建类型
     */
	@TableField("corptype")
	private String corpType;
    /**
     * 进场时间
     */
	@TableField("intime")
	private Date inTime;
    /**
     * 出场时间
     */
	@TableField("outtime")
	private Date outTime;
    /**
     * 原项目id
     */
	private String projectid;
	/**
	 * 项目id
	 */
	private String pj0101;
	/**
	 * 参建单位id
	 */
	private String cp0201;
}