package com.xygk.applet.common.task;

import com.xygk.applet.config.sms.dao.SysSmsTaskDao;
import com.xygk.applet.config.sms.entity.SysSmsTaskEntity;
import com.xygk.applet.config.sms.service.SysSmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title BeingPushedTask
 * @Description 短信发送任务
 * @Date 2021/10/18 16:46
 * @Copyright 2019-2025
 */
@Component
@EnableScheduling
public class SmsTask {

    @Autowired
    private SysSmsTaskDao taskDao;
    @Autowired
    private SysSmsService sysSmsService;


    /**
     * 每五分钟处理一次待办短信任务
     */
    //@Scheduled(cron = "0 0/5 * * * ? ")
    public void sendSmsTask() {
        //查询待发送短信任务
        List<SysSmsTaskEntity> list = taskDao.selectListForOrder();
        for (SysSmsTaskEntity task : list) {
            //发送通知短信
            sysSmsService.send(task.getSmsCode(), task.getMobile(), task.getContent());
            task.setStatus("1");
            task.setSendDate(new Date());
            taskDao.updateById(task);
        }
    }

}

