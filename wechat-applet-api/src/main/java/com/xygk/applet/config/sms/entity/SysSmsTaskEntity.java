/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.xygk.applet.config.sms.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 短信任务
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_sms_task")
public class SysSmsTaskEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 短信编码
     */
    private String smsCode;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 短信内容
     */
    private String content;
    /**
     * 处理状态
     */
    private String status;

    /**
     * 发送时间
     */
    private Date sendDate;

}