package com.xygk.applet.config.sms.service;

import com.xygk.applet.config.sms.dto.SysSmsLogDTO;
import com.xygk.applet.config.sms.entity.SysSmsLogEntity;
import io.renren.common.service.CrudService;

import java.util.LinkedHashMap;

/**
 * 短信日志
 *
 * <AUTHOR>
 */
public interface SysSmsLogService extends CrudService<SysSmsLogEntity, SysSmsLogDTO> {

    /**
     * 保存短信发送记录
     * @param smsCode   短信编码
     * @param platform  平台
     * @param mobile    手机号
     * @param params    短信参数
     * @param status    发送状态
     */
    void save(String smsCode, Integer platform, String mobile, LinkedHashMap<String, String> params, Integer status);
}