package com.xygk.applet.project.controller;

import com.xygk.applet.common.annotation.Login;
import com.xygk.applet.common.enums.RoleEnum;
import com.xygk.applet.modules.dto.Kq02GetFailedIssuedDTO;
import com.xygk.applet.modules.service.DeviceService;
import com.xygk.applet.project.dto.AuthorDTO;
import com.xygk.applet.project.dto.DeviceDTO;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title HomeController
 * @Description 考勤记录接口（项目端）
 * @Date 2022/12/27 16:24
 * @Copyright 2019-2025
 */
@RestController
@RequestMapping("/api/project/device")
@Api(tags = "设备下发接口（项目端）")
public class DeviceController {
    @Autowired
    private DeviceService deviceService;

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getList")
    @ApiOperation("获取设备下发记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
    })
    public Result getList(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        List<DeviceDTO> list = deviceService.getList(params);
        return new Result().ok(list);
    }

    @Login(role = RoleEnum.PROJECT)
    @PostMapping("issue")
    @ApiOperation("重新下发人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "personType", value = "人员类型", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "人员ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sn", value = "设备序列号", paramType = "query", dataType = "String"),
    })
    public Result issue(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        deviceService.issue(params);
        return new Result().ok("下发成功！");
    }

    @PostMapping("changeHeadImage")
    @ApiOperation("更换人员头像")
    public Result changeHeadImage(@ApiParam(name = "file", value = "头像", required = true) @RequestParam("file") MultipartFile file,
                                  @ApiParam(name = "userId", value = "人员ID", required = true) @RequestParam("userId") Long userId,
                                  @ApiParam(name = "personType", value = "人员类型", required = true) @RequestParam("personType") String personType) {
        AssertUtils.isNull(file, "上传文件不能为空！");
        String headpath = deviceService.changeHeadImage(file, userId, personType);
        return new Result().ok(headpath);
    }

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("/getFailedIssued")
    @ApiOperation("获取当前设备下发失败人员名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
    })
    public Result<PageData<Kq02GetFailedIssuedDTO>> getFailedIssued(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Kq02GetFailedIssuedDTO> getFailedIssued = deviceService.getFailedIssued(pj0101, params);
        return new Result<PageData<Kq02GetFailedIssuedDTO>>().ok(getFailedIssued);
    }

    @Login(role = RoleEnum.PROJECT)
    @PostMapping("/failedIssuedToDevice")
    @ApiOperation("失败人员批量下发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "人员名称", paramType = "query", dataType = "String"),
    })
    public Result failedIssuedToDevice(@RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestBody AuthorDTO dto) {
        dto.setPj0101(pj0101);
        return deviceService.failedIssuedToDevice(dto);
    }
}
