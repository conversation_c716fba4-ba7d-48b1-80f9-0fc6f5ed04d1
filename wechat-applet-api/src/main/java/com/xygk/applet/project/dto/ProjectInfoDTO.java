package com.xygk.applet.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@ApiModel(value = "项目信息")
public class ProjectInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参建单位数量")
    private String companys;

    @ApiModelProperty(value = "班组数量")
    private String teams;

    @ApiModelProperty(value = "管理人员数量")
    private String managers;

    @ApiModelProperty(value = "建筑工人数量")
    private String workers;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "项目状态")
    private String prjstatus;

    @ApiModelProperty(value = "主管部门")
    private String virareacode;

    @ApiModelProperty(value = "建设地址（项目的详细地址具体到XX街道XX号）")
    private String address;

    @ApiModelProperty(value = "开工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "竣工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    @ApiModelProperty(value = "联系人姓名")
    private String linkman;

    @ApiModelProperty(value = "项目规模")
    private String category;

    @ApiModelProperty(value = "是否需要完善项目信息")
    private String iscomplete;
}