package com.xygk.applet.modules.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备任务待办表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-09
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("SUP_DEVICE_TASK")
public class SupDeviceTaskEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long id;
    /**
     * 设备序列号
     */
	private String deviceKey;
    /**
     * 数据类型(1删除，2下发)
     */
	private Integer type;
    /**
     * 待处理状态(-1待处理，0成功，1失败)
     */
	private Integer result;
    /**
     * 返回结果
     */
	private String msg;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}
