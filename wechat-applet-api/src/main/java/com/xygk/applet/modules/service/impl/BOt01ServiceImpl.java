package com.xygk.applet.modules.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.applet.common.constant.SystemConstant;
import com.xygk.applet.common.util.FilesUtils;
import com.xygk.applet.common.util.QRCodeUtils;
import com.xygk.applet.modules.dao.BOt01Dao;
import com.xygk.applet.modules.dao.BPs02Dao;
import com.xygk.applet.modules.dto.BOt01DTO;
import com.xygk.applet.modules.entity.BOt01Entity;
import com.xygk.applet.modules.service.BOt01Service;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.file.FileProper;
import io.renren.common.file.FilesUploadUtil;
import io.renren.common.minio.MinIoUtil;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 附件数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-14
 */
@Service
public class BOt01ServiceImpl extends CrudServiceImpl<BOt01Dao, BOt01Entity, BOt01DTO> implements BOt01Service {

    @Autowired
    private BPs02Dao ps02Dao;

    private static String firstPath;

    public BOt01ServiceImpl(FileProper properties) {
        BOt01ServiceImpl.firstPath = properties.getPath();
    }

    @Override
    public QueryWrapper<BOt01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<BOt01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public Result<Long> uploadFile(MultipartFile file, String busitype, Long busisysno) {
        String folder = null;
        switch (busitype) {
            case "01":
                folder = Constant.FileType.PROJECTCONTRACT.Type();
                break;
            case "02":
                folder = Constant.FileType.PROJECTARCHIVES.Type();
                break;
            case "03":
                folder = Constant.FileType.WORKERPAYROLL.Type();
                break;
            case "04":
                folder = Constant.FileType.PROJECTWARN.Type();
                break;
            case "10":
                folder = Constant.FileType.WORKERCONTRACT.Type();
                break;
            default:
                folder = "other";
        }
        String filePath = FilesUploadUtil.uploadFileFolder(file, folder);
        if (busitype.equals(Constant.FileType.WORKERCONTRACT.Value())) {
            //校验合同照片是否存在问题
            File nfile = new File(firstPath + filePath);
            String code = null;
            try {
                code = QrCodeUtil.decode(FileUtil.file(firstPath + filePath));
            } catch (Exception e) {
                throw new RenException("图中没有二维码或请调整照片分辨率！");
            }
            //String code = QRCodeUtils.deEncodeByPath(nfile);
            if (StringUtils.isEmpty(code)) {
                nfile.delete();
                throw new RenException("未识别出图片中二维码信息！");
            }
            //获取工人合同编号
            String contractno = ps02Dao.selectContractNoById(busisysno);
            if (!code.equals(String.valueOf(contractno)) || contractno == null) {
                nfile.delete();
                throw new RenException("合同照片信息与工人不一致，请确认上传的合同照片是否有误。");
            }
        }
        BOt01Entity ot01Entity = new BOt01Entity();
        ot01Entity.setName(FileUtil.getName(filePath));
        ot01Entity.setBusitype(busitype);
        ot01Entity.setUrl(filePath);
        ot01Entity.setOriginalName(file.getOriginalFilename());
        ot01Entity.setWhether("1");
        baseDao.insert(ot01Entity);
        return new Result<Long>().ok(ot01Entity.getOt0101());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadContractFile(Long ps0201, MultipartFile image) {
        //保存附件
        String filePath = null;
        try {
            filePath = FilesUtils.saveFileToLocal(SystemConstant.CONTRACTIMAGE, image);
        } catch (IOException e) {
            throw new RenException("文件保存失败！");
        }
        //校验合同照片是否存在问题
        File file = new File(filePath);
        String code = QRCodeUtils.deEncodeByPath(file);
        if (StringUtils.isEmpty(code)) {
            file.delete();
            throw new RenException("未识别出图片中二维码信息！");
        }
        if (!code.equals(String.valueOf(ps0201))) {
            file.delete();
            throw new RenException("合同照片信息与工人不一致，请确认上传的合同照片是否有误。");
        }
        //保存附件信息
        BOt01Entity bOt01Entity = new BOt01Entity();
        bOt01Entity.setBusitype("10");
        bOt01Entity.setBusisysno(ps0201);
        bOt01Entity.setName(file.getName());
        bOt01Entity.setUrl(filePath.replace(FilesUtils.filePath, "/"));
        bOt01Entity.setViewType("img");
        bOt01Entity.setWhether("1");
        bOt01Entity.setCreateDate(new Date());
        baseDao.insert(bOt01Entity);
        return bOt01Entity.getOt0101();
    }

    @Override
    public List<BOt01DTO> getFiles(String busitype, Long busisysno) {
        return baseDao.getFiles(busitype, busisysno);
    }

    @Override
    public List<BOt01DTO> getBusinessData(Long busisysno, String busitype) {
        return baseDao.getBusinessData(busisysno, busitype);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long ot0101) {
        baseDao.deleteById(ot0101);
    }

    /**
     * 整理文件
     *
     * @param ot0101List 文件数据
     * @param bussino    业务类型
     */
    @Override
    public void doFileRelation(List<BOt01DTO> ot0101List, Long bussino) {
        if (CollectionUtil.isEmpty(ot0101List)) {
            return;
        }
        //过滤出业务id为空的数据
        List<Long> ot0101Lists = ot0101List
                .stream()
                .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                .map(BOt01DTO::getOt0101)
                .collect(Collectors.toList());
        if (ot0101Lists.size() > 0) {
            updateBusily(bussino, ot0101Lists);
        }
    }

    @Override
    public void updateUnlessFiles(String busitype, Long busisysno) {
        baseDao.updateUnlessFiles(busitype, busisysno);
    }

    @Override
    public void updateBusily(Long busies, List<Long> ot0101) {
        baseDao.updateByIdBusily(busies, ot0101);
    }

    @Override
    public Long upload(MultipartFile file, String fileType, String viewType) {
        String saveFileName = MinIoUtil.upload(file, "normal");
        BOt01Entity ot01Entity = new BOt01Entity();
        ot01Entity.setName(FileUtil.getName(saveFileName));
        ot01Entity.setBusitype(fileType);
        ot01Entity.setUrl(saveFileName);
        ot01Entity.setViewType(viewType);
        ot01Entity.setOriginalName(file.getOriginalFilename());
        ot01Entity.setWhether("1");
        baseDao.insert(ot01Entity);
        return ot01Entity.getOt0101();
    }

    @Override
    public String uploadForNetUrl(MultipartFile file) {
        String saveFileName = MinIoUtil.upload(file, "normal");
        return saveFileName;
    }
}