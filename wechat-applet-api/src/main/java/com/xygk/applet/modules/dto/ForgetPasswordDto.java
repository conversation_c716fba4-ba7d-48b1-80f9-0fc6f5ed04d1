package com.xygk.applet.modules.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @title ChangePasswordDto
 * @Description 密码修改Dto
 * @Date 2021/10/14 10:17
 * @Copyright 2019-2025
 */
@Data
@ApiModel(value = "密码修改")
public class ForgetPasswordDto {
    @ApiModelProperty("手机号")
    @NotNull
    private String cellphone;
    @NotNull
    @Max(value = 20, message = "密码最大长度不能超过20")
    private String newPassword;
    @NotNull
    @Max(value = 20, message = "密码最大长度不能超过20")
    private String confirmPassword;
    @ApiModelProperty(value = "短信验证码", required = true)
    @NotNull(message = "验证码不能为空")
    private String smsCode;
}
