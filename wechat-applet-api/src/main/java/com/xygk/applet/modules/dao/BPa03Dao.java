package com.xygk.applet.modules.dao;

import com.xygk.applet.modules.dto.WagesDto;
import com.xygk.applet.modules.entity.BPa03Entity;
import com.xygk.applet.supervision.dto.WagesInfoDto;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-15
 */
@Mapper
public interface BPa03Dao extends BaseDao<BPa03Entity> {
    /**
     *
     * @description 通过项目ID获取个人工资记录
     * <AUTHOR>
     * @date 2021年10月18日 10:07
     */
    List<WagesDto> getWagesList(Map<String, Object> params);
    /**
     *
     * @description 获取工人工资信息（监管端）
     * <AUTHOR>
     * @date 2022年12月27日 15:47
     */
    List<WagesInfoDto> getWagesInfo(Map<String, Object> params);
}
