package com.xygk.applet.modules.dao;


import com.xygk.applet.modules.dto.BPs06DTO;
import com.xygk.applet.modules.entity.BPs06Entity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-04-27
 */
@Mapper
public interface BPs06Dao extends BaseDao<BPs06Entity> {

    /**
     * 批量写入进退场记录
     *
     * @param dtoList
     */
    void batchInsertInOrOut(List<BPs06DTO> dtoList);
}