package com.xygk.applet.modules.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-15
 */
@Data
@ApiModel(value = "工资代发明细表")
public class BPa03DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long pa0301;
    @ApiModelProperty(value = "专户流水ID")
    private Long pa0201;
    @ApiModelProperty(value = "金额")
    private BigDecimal accountnum;
    @ApiModelProperty(value = "对方账号")
    private String partaccount;
    @ApiModelProperty(value = "对方户名")
    private String partname;
    @ApiModelProperty(value = "备注")
    private String description;

}