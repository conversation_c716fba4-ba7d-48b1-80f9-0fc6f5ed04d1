package com.xygk.applet.modules.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xygk.applet.modules.dao.BOt01Dao;
import com.xygk.applet.modules.dao.BPs02Dao;
import com.xygk.applet.modules.dao.BPs08Dao;
import com.xygk.applet.modules.dao.Pj01Dao;
import com.xygk.applet.modules.dto.*;
import com.xygk.applet.modules.entity.BPs02Entity;
import com.xygk.applet.modules.entity.Pj01Entity;
import com.xygk.applet.modules.service.BPs02Service;
import com.xygk.applet.modules.service.IPs02FaceService;
import com.xygk.applet.modules.service.SysUserService;
import com.xygk.applet.modules.vo.FaceRecoResVO;
import com.xygk.applet.supervision.dto.EmploymentInfoDto;
import com.xygk.applet.supervision.dto.WorkerInfoDetailsDto;
import com.xygk.applet.supervision.dto.WorkerInfoDto;
import com.xygk.applet.supervision.dto.WorkerPageInfoDto;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-14
 */
@Service
public class BPs02ServiceImpl extends CrudServiceImpl<BPs02Dao, BPs02Entity, BPs02DTO> implements BPs02Service {
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    IPs02FaceService iPs02FaceService;
    @Autowired
    private BOt01Dao ot01Dao;
    @Autowired
    private BPs08Dao ps08Dao;
    @Autowired
    private Pj01Dao pj01Dao;

    @Override
    public QueryWrapper<BPs02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<BPs02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<BPs02DTO> pageList(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<WorkerInfoDto> getWorkerInfoList(Map<String, Object> params) {
        //获取机构ID
        Long userId = Long.valueOf(params.get("userId").toString());
        params.put("deptId", sysUserService.getDeptId(userId));
        IPage<BPs02Entity> page = getPage(params, "", false);
        List<WorkerInfoDto> list = baseDao.getWorkerInfoList(params);
        return getPageData(list, page.getTotal(), WorkerInfoDto.class);
    }

    @Override
    public WorkerInfoDetailsDto getWorkerInfo(Long ps0201) {
        //查询工人信息
        WorkerInfoDetailsDto workerInfoDetailsDto = baseDao.getWorkerInfo(ps0201);
        //查询参建单位信息
        workerInfoDetailsDto.setParticipatingInfoDto(baseDao.getParticipatingInfo(ps0201));
        return workerInfoDetailsDto;
    }

    @Override
    public Result<BPs08DTO> getContractInfo(Long ps0201) {
        Result<BPs08DTO> result = new Result<>();
        BPs08DTO dto = ps08Dao.getContractInfo(ps0201);
        if (dto != null) {
            //查询合同附件信息
            List<BOt01DTO> files = ot01Dao.getFiles(Constant.FileType.WORKERCONTRACT.Value(), ps0201);
            dto.setContractFiles(files);
        }
        return result.ok(dto);
    }

    @Override
    public PageData<EmploymentInfoDto> getEmploymentInfo(Map<String, Object> params) {
        IPage<BPs02Entity> page = getPage(params, "", false);
        List<EmploymentInfoDto> list = baseDao.getEmploymentInfo(params);
        return getPageData(list, page.getTotal(), EmploymentInfoDto.class);
    }

    @Override
    public PageData<WorkerPageInfoDto> getWorkerPageInfoList(Map<String, Object> params) {
        IPage<BPs02Entity> page = getPage(params, "", false);
        List<WorkerPageInfoDto> list = baseDao.getWorkerPageInfoList(params);
        return getPageData(list, page.getTotal(), WorkerPageInfoDto.class);
    }

    @Override
    public PageData<WorkerInfoDto> getWorkerPageInfoListByTm0101(Map<String, Object> params) {
        IPage<BPs02Entity> page = getPage(params, "", false);
        List<WorkerInfoDto> list = baseDao.getWorkerPageInfoListByTm0101(params);
        return getPageData(list, page.getTotal(), WorkerInfoDto.class);
    }

    @Override
    public List<WorkerInfoDetailsDto> getWorkerQueryList(Map<String, Object> params) {
        ArrayList<WorkerInfoDetailsDto> list = new ArrayList<>();
        FaceRecoResVO faceDto = new FaceRecoResVO();
        String faceImage = MapUtil.getStr(params, "faceImage");
        Long pj0101 = MapUtil.getLong(params, "pj0101");
        String areacode = MapUtil.getStr(params, "areacode");
        if (StrUtil.isBlank(areacode)) {
            Pj01Entity pj01Entity = pj01Dao.selectById(pj0101);
            if (ObjectUtil.isNotNull(pj01Entity)) {
                areacode = pj01Entity.getAreacode();
            }
        }
        faceDto.setImage(faceImage);
        faceDto.setProjectId(pj0101);
        faceDto.setAreacode(areacode);

        Result<List<FaceCheckedResDTO>> result = iPs02FaceService.faceSearch(faceDto);
        List<FaceCheckedResDTO> faceList = result.getData();
        if (CollectionUtil.isEmpty(faceList)) {
            throw new RenException("系统中未查询到此人员信息");
        }
        for (FaceCheckedResDTO dto : faceList) {
            WorkerInfoDetailsDto workerInfo = baseDao.getWorkerInfo(Long.valueOf(dto.getPs0201()));
            if (ObjectUtil.isNotNull(workerInfo)) {
                list.add(workerInfo);
            }
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new RenException("系统中未查询到此人员信息");
        }
        return list;
    }

    /**
     * 获取人脸查找列表
     *
     * @param params
     * @return
     */
    @Override
    public List<WorkerInfoSearchWorkerDto> searchWorkerList(Map<String, Object> params) {
        ArrayList<WorkerInfoSearchWorkerDto> list = new ArrayList<>();
        FaceRecoResVO faceDto = new FaceRecoResVO();
        String faceImage = MapUtil.getStr(params, "faceImage");
        Long pj0101 = MapUtil.getLong(params, "pj0101");
        String areacode = MapUtil.getStr(params, "areacode");
        if (StrUtil.isBlank(areacode)) {
            Pj01Entity pj01Entity = pj01Dao.selectById(pj0101);
            if (ObjectUtil.isNotNull(pj01Entity)) {
                areacode = pj01Entity.getAreacode();
            }
        }
        faceDto.setImage(faceImage);
        faceDto.setProjectId(pj0101);
        faceDto.setAreacode(areacode);

        Result<List<FaceCheckedResDTO>> result = iPs02FaceService.faceSearch(faceDto);
        List<FaceCheckedResDTO> faceList = result.getData();
        if (CollectionUtil.isEmpty(faceList)) {
            throw new RenException("系统中未查询到此人员信息");
        }

        List<WorkerInfoSearchWorkerDto> workerInfoSearchWorkerDtos = baseDao.searchWorkerList(faceList);

        if (CollectionUtil.isEmpty(workerInfoSearchWorkerDtos)) {
            throw new RenException("系统中未查询到此人员信息");
        }
        return workerInfoSearchWorkerDtos;
    }
}
