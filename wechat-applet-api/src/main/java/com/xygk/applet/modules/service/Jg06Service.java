package com.xygk.applet.modules.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.applet.modules.dto.Jg06DTO;
import com.xygk.applet.modules.entity.Jg06Entity;
import com.xygk.applet.supervision.dto.AuditSupervisoryAccountDto;
import com.xygk.applet.supervision.dto.SupervisoryAccountListDto;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;

import java.util.Map;

/**
 * 监管注册表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-04
 */
public interface Jg06Service extends CrudService<Jg06Entity, Jg06DTO> {
    /**
     *
     * @description 查询一条账号注册信息
     * <AUTHOR>
     * @date 2023年01月05日 9:25
     */
    Jg06Entity selectOne(QueryWrapper<Jg06Entity> qw);
    /**
     *
     * @description 获取监管账户注册信息列表（监管端）
     * <AUTHOR>
     * @date 2023年01月05日 9:25
     */
    PageData<SupervisoryAccountListDto> getSupervisoryAccountList(Map<String, Object> params);
    /**
     *
     * @description 审核监管账号（监管端）
     * <AUTHOR>
     * @date 2023年01月05日 10:40
     */
    void auditSupervisoryAccount(AuditSupervisoryAccountDto dto);
}
