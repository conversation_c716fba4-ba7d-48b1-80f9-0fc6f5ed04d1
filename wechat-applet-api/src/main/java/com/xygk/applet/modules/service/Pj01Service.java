package com.xygk.applet.modules.service;

import com.xygk.applet.modules.dto.Pj01DTO;
import com.xygk.applet.modules.dto.ProjectListDto;
import com.xygk.applet.modules.dto.RegionDTO;
import com.xygk.applet.modules.entity.Pj01Entity;
import com.xygk.applet.supervision.dto.ParticipationProjectInfoDto;
import com.xygk.applet.supervision.dto.ProjectInfoDto;
import com.xygk.applet.supervision.dto.ProjectPageInfoDto;
import com.xygk.applet.supervision.dto.StatisticsListDTO;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-19
 */
public interface Pj01Service extends CrudService<Pj01Entity, Pj01DTO> {
    /**
     *
     * @description 获取项目列表
     * <AUTHOR>
     * @date 2022年11月19日 10:30
     */
    List<ProjectListDto> getProjectList(Long userId);
    /**
     *
     * @description 获取企业参建项目信息列表（监管端）
     * <AUTHOR>
     * @date 2022年12月28日 13:58
     */
    PageData<ProjectPageInfoDto> getParticipationProjectInfo(Map<String, Object> params);
    /**
     *
     * @description 获取项目分页列表数据（监管端）
     * <AUTHOR>
     * @date 2022年12月29日 13:59
     */
    PageData<ProjectPageInfoDto> getProjectInfoList(Map<String, Object> params);
    /**
     *
     * @description 获取项目详情信息（监管端）
     * <AUTHOR>
     * @date 2022年12月29日 15:13
     */
    ProjectInfoDto getProjectInfo(Long pj0101);
    /**
     *
     * @description 根据项目ids查询项目信息（监管端）
     * <AUTHOR>
     * @date 2023年01月09日 10:29
     */
    List<ProjectPageInfoDto> selectListByIds(String[] pj0101s);
    /**
     *
     * @description 根据项目ids查询项目信息
     * <AUTHOR>
     * @date 2023年01月09日 14:53
     */
    List<Pj01Entity> selectBatchIds(ArrayList<String> toList);
    /**
     *
     * @description 获取项目电子围栏经纬度信息
     * <AUTHOR>
     * @date 2023年01月13日 10:42
     */
    Pj01Entity getProjectRegion(Long pj0101);

    /**
     * 电子围栏审核列表
     * @param params
     * @return
     */
    PageData<RegionDTO> pageList(Map<String, Object> params);

    /**
     * 电子围栏审核详情
     * @param params
     * @return
     */
    Result getInfo(Map<String, Object> params);

    /**
     * 电子围栏审核
     * @param dto
     * @return
     */
    Result audit(Long userId, RegionDTO dto);

    /**
     * 首页基本统计数据
     * @param params
     * @return
     */
    StatisticsListDTO getStatisticsList(Map<String, Object> params);
}
