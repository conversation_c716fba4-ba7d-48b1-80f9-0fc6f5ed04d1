package com.xygk.applet.modules.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目培训信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-21
 */
@Data
@ApiModel(value = "项目培训信息")
public class Pj03DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0301;
    @ApiModelProperty(value = "主键ID")
    private Long pj0401;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String projectname;
    @ApiModelProperty(value = "课程名称")
    private String trainingname;
    @ApiModelProperty(value = "培训类型")
    private String trainingtypecode;
    @ApiModelProperty(value = "培训方式")
    private String trainingmode;
    @ApiModelProperty(value = "培训讲师")
    private String trainer;
    @ApiModelProperty(value = "讲师证件号码")
    private String traineridcardnumber;
    @ApiModelProperty(value = "培训机构")
    private String trainingorg;
    @ApiModelProperty(value = "培训地址")
    private String trainingaddress;
    @ApiModelProperty(value = "培训开始时间")
    private Date trainingstartdate;
    @ApiModelProperty(value = "培训结束时间")
    private Date trainingenddate;
    @ApiModelProperty(value = "培训时长")
    private BigDecimal trainingduration;
    @ApiModelProperty(value = "培训简述")
    private String description;
    @ApiModelProperty(value = "培训教材")
    private String textbook;
    @ApiModelProperty(value = "建筑工人ID")
    private Long ps0201;
    @ApiModelProperty(value = "是否合格")
    private String ispass;
    @ApiModelProperty(value = "培训得分(分值0~100，可以保留1位小数)")
    private BigDecimal score;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "培训资料")
    private List<BOt01DTO> files;

    @ApiModelProperty(value = "是否能作答， 0可以 1不行")
    private Integer isanswer;

    @ApiModelProperty(value = "最后得分")
    private String lastscore;

    @ApiModelProperty(value = "最后结果")
    private String lastispass;
}