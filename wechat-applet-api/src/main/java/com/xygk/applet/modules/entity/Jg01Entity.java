package com.xygk.applet.modules.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预警表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_JG01")
public class Jg01Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long jg0101;
    /**
     * 项目主键ID
     */
    private Long pj0101;
    /**
     * 预警信息
     */
    private String warinfo;
    /**
     * 状态(dic)（1、预警中 2、处理中 3、已处理 ）
     */
    private String warstatus;
    /**
     * 处理意见
     */
    private String opinions;
    /**
     * 预警类型（见JG02）
     */
    private String wartype;
    /**
     * 预警开始时间
     */
    private Date starttime;
    /**
     * 预警消除时间
     */
    private Date endtime;
    /**
     * 流转状态
     */
    private Short processtatus;
    /**
     * 最大状态
     */
    private Short maxprocess;
    /**
     * 预警相关角色
     */
    private String relationrole;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
