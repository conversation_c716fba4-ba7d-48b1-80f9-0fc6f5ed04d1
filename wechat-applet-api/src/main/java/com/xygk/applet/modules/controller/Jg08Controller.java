package com.xygk.applet.modules.controller;

import com.xygk.applet.modules.dto.Jg08DTO;
import com.xygk.applet.modules.service.Jg08Service;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 工资投诉表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-16
 */
@RestController
@RequestMapping("/api/jg08")
@Api(tags = "工资投诉表")
public class Jg08Controller {
    @Autowired
    private Jg08Service jg08Service;


    @GetMapping("getInfo")
    @ApiOperation("信息")
    public Result<Jg08DTO> getInfo(@ApiIgnore @RequestParam Map<String, Object> params) {
        Long jg0801 = Long.valueOf(params.get("jg0801").toString());
        Result result = jg08Service.getInfo(jg0801);

        return result;
    }

}