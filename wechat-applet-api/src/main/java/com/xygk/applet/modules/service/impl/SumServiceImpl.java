package com.xygk.applet.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xygk.applet.modules.dao.SumDao;
import com.xygk.applet.modules.service.SumService;
import com.xygk.applet.modules.service.SysDeptService;
import com.xygk.applet.project.dto.ProjectInfoDTO;
import com.xygk.applet.supervision.dto.*;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SumServiceImpl implements SumService {
    @Autowired
    private SumDao sumDao;
    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public PageData<ProjectInfoDto> areacodePage(Long userId, Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        String areacode = (String) params.get("areacode");
        if (StringUtils.isBlank(areacode)) {
            areacode = sysDeptService.getDeptByUserId(userId).getAreacode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
        }
        Page<ProjectInfoDTO> page = new Page<>(curPage, limit);
        List<ProjectInfoDTO> list = sumDao.areacodePage(params);
        ProjectInfoDTO areacodeDto = sumDao.areacodeSum(params);
        list.add(areacodeDto);
        return getPageData(list, page.getTotal(), ProjectInfoDTO.class);
    }

    @Override
    public PageData<WorkerDTO> workerPage(Long userId,Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<WorkerDTO> page = new Page<>(curPage, limit);
        List<WorkerDTO> list = sumDao.workerPage(page, params);
        return getPageData(list, page.getTotal(), WorkerDTO.class);
    }

    @Override
    public PageData<ProjectDTO> projectPage(Long userId,Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        String areacode = (String) params.get("areacode");
        if (StringUtils.isBlank(areacode)) {
            areacode = sysDeptService.getDeptByUserId(userId).getAreacode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
        }
        Page<ProjectDTO> page = new Page<>(curPage, limit);
        List<ProjectDTO> list = sumDao.projectPage(page, params);
        return getPageData(list, page.getTotal(), ProjectDTO.class);
    }

    @Override
    public PageData<SalaryDTO> salaryPage(Long userId,Map<String, Object> params) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String year = sdf.format(new Date());
        String quarter = params.get("quarter").toString();
        switch (quarter) {
            case "1":
                params.put("startdate", year + "01");
                params.put("enddate", year + "03");
                params.put("quarter", Constant.Quarter.FIRST.Describe());
                break;
            case "2":
                params.put("startdate", year + "04");
                params.put("enddate", year + "06");
                params.put("quarter", Constant.Quarter.SECOND.Describe());
                break;
            case "3":
                params.put("startdate", year + "07");
                params.put("enddate", year + "09");
                params.put("quarter", Constant.Quarter.THIRD.Describe());
                break;
            case "4":
                params.put("startdate", year + "10");
                params.put("enddate", year + "12");
                params.put("quarter", Constant.Quarter.FOURTH.Describe());
                break;
            default:
        }
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        String areacode = (String) params.get("areacode");
        if (StringUtils.isBlank(areacode)) {
            areacode = sysDeptService.getDeptByUserId(userId).getAreacode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
        }
        Page<SalaryDTO> page = new Page<>(curPage, limit);
        List<SalaryDTO> list = sumDao.salaryPage(page, params);
        return getPageData(list, page.getTotal(), SalaryDTO.class);
    }

    @Override
    public PageData<AttendanceDTO> attendancePage(Long userId,Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        String areacode = (String) params.get("areacode");
        if (StringUtils.isBlank(areacode)) {
            areacode = sysDeptService.getDeptByUserId(userId).getAreacode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
        }
        Page<AttendanceDTO> page = new Page<>(curPage, limit);
        List<AttendanceDTO> list = sumDao.attendancePage(params);
        AttendanceDTO attendanceDto = sumDao.attendanceSum(params);
        list.add(attendanceDto);
        return getPageData(list, page.getTotal(), AttendanceDTO.class);
    }

    protected PageData getPageData(List list, long total, Class dtoClass) {
        List targetList = ConvertUtils.sourceToTarget(list, dtoClass);
        return new PageData<>(targetList, total);
    }
}