/**
 * Copyright (c) 2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */
package com.xygk.applet.modules.dao;

import com.xygk.applet.modules.entity.SysNoticeUserEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * 我的通知
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Mapper
public interface SysNoticeUserDao extends BaseDao<SysNoticeUserEntity> {

    String selectInfo(Long id, Long userId);

    void updateReadStatus(Long userId, Long noticeid);
}