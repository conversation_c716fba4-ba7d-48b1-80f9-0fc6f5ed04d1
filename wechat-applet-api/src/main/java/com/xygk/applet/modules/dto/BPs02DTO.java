package com.xygk.applet.modules.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-14
 */
@Data
@ApiModel(value = "建筑工人信息")
public class BPs02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;
    @ApiModelProperty(value = "人员ID")
    private Long ps0101;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @ApiModelProperty(value = "人员姓名")
    private String name;
    @ApiModelProperty(value = "是否班组长")
    private String isteamleader;
    @ApiModelProperty(value = "工种")
    private String worktypecode;
    @ApiModelProperty(value = "工资卡帐号")
    private String payrollbankcardnumber;
    @ApiModelProperty(value = "工资卡开户行名称")
    private String payrollbankname;
    @ApiModelProperty(value = "工资卡银行代码")
    private String payrolltopbankcode;
    @ApiModelProperty(value = "工人头像")
    private String issuecardpicurl;
    @ApiModelProperty(value = "是否购买工伤或意外伤害保险 ")
    private String hasbuyinsurance;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;

}