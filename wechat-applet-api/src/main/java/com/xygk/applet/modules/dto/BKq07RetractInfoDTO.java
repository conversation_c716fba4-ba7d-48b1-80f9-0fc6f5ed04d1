package com.xygk.applet.modules.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 撤回请假申请
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2023/11/9 12:01
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class BKq07RetractInfoDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 请假记录id
     */
    @NotBlank(message = "请选择一条请假记录")
    private String kq0701;

    /**
     * 项目id
     */
    @NotBlank(message = "请选择你所在的项目")
    private String pj0101;
}
