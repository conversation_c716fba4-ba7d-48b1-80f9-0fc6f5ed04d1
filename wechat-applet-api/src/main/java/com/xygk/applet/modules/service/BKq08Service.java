package com.xygk.applet.modules.service;

import com.xygk.applet.modules.dto.BKq08DTO;
import com.xygk.applet.modules.dto.BKq08RetractAuditDTO;
import com.xygk.applet.modules.dto.BKq08WaitAuditDTO;
import com.xygk.applet.modules.dto.BKq08WorkerApplyToDoDetailDTO;
import com.xygk.applet.modules.entity.BKq08Entity;
import io.renren.common.service.CrudService;

import java.util.List;
import java.util.Map;

/**
 *
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-18
 */
public interface BKq08Service extends CrudService<BKq08Entity, BKq08DTO> {

    /**
     * 写入处置明细数据
     *
     * @param list
     */
    void insertBatchApplyTask(List<BKq08Entity> list);

    /**
     * 获取审核人信息
     *
     * @param bkq0701
     * @return
     */
    List<BKq08WaitAuditDTO> getKq08WaitAuditDTO(Long bkq0701);

    /**
     * 获取审核列表-待办任务
     *
     * @param bkq0701
     * @return
     */
    List<BKq08RetractAuditDTO> getKq08RetractWaitAuditDTO(Long bkq0701);

    /**
     * 获取代办申请详情
     *
     * @param params
     * @return
     */
    BKq08WorkerApplyToDoDetailDTO getWorkerApplyToDoDetail(Map<String, Object> params);

    /**
     * 检查整个审核流程是否已经完成审核并且都是通过
     *
     * @param kq0701
     * @return
     */
    int checkApplyProcess(Long kq0701);

    /**
     * 检查撤回状态
     *
     * @param kq0701
     * @return
     */
    Integer checkRetractApply(Long kq0701);
}