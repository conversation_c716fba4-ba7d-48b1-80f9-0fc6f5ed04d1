package com.xygk.applet.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xygk.applet.modules.dao.BKq02Dao;
import com.xygk.applet.modules.dao.SysUserDao;
import com.xygk.applet.modules.dto.SysUserDTO;
import com.xygk.applet.modules.entity.SysUserEntity;
import com.xygk.applet.modules.service.AttendanceService;
import com.xygk.applet.project.dto.AttendanceArriveDTO;
import com.xygk.applet.project.dto.AttendanceDTO;
import com.xygk.applet.project.dto.AttendancePersonTjDTO;
import com.xygk.applet.project.dto.AttendanceTeamDTO;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通用方法
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AttendanceServiceImpl extends CrudServiceImpl<SysUserDao, SysUserEntity, SysUserDTO> implements AttendanceService {
    @Autowired
    private BKq02Dao kq02Dao;

    @Override
    public QueryWrapper<SysUserEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<AttendanceDTO> getList(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<AttendanceDTO> list = kq02Dao.getList(params);
        return getPageData(list, page.getTotal(), AttendanceDTO.class);
    }

    @Override
    public PageData<AttendanceDTO> getListByWorker(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<AttendanceDTO> list = kq02Dao.getList(params);
        return getPageData(list, page.getTotal(), AttendanceDTO.class);
    }

    @Override
    public Result getPersonStatistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        JSONArray list = new JSONArray();
        for (int i = 0; i < 3; i++) {
            String month = DateUtil.format(DateUtil.offsetMonth(new Date(), -i), "yyyy-MM");
            params.put("month", month);
            AttendancePersonTjDTO dto = kq02Dao.getPersonStatistics(params);
            JSONObject object = new JSONObject();
            object.put("month", month);
            object.put("kqs", dto.getKqs());
            object.put("arrivepercent", dto.getArrivepercent());
            list.add(object);
        }
        return result.ok(list);
    }

    @Override
    public Result getPersonStatisticsByWorker(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        JSONArray list = new JSONArray();
        for (int i = 0; i < 3; i++) {
            String month = DateUtil.format(DateUtil.offsetMonth(new Date(), -i), "yyyy-MM");
            params.put("month", month);
            AttendancePersonTjDTO dto = kq02Dao.getPersonStatistics(params);
            JSONObject object = new JSONObject();
            object.put("month", month);
            object.put("kqs", dto.getKqs());
            object.put("arrivepercent", dto.getArrivepercent());
            list.add(object);
        }
        return result.ok(list);
    }

    @Override
    public Result getCalendarList(Map<String, Object> params) {
        JSONObject object = new JSONObject();
        List<AttendancePersonTjDTO> list = kq02Dao.getCalendarList(params);
        AttendancePersonTjDTO dto = kq02Dao.getPersonStatistics(params);
        object.put("list", list);
        object.put("arrivepercent", dto.getArrivepercent());
        object.put("kqs", dto.getKqs());
        return new Result().ok(object);
    }

    @Override
    public Result getCalendarListByWorker(Map<String, Object> params) {
        JSONObject object = new JSONObject();
        List<AttendancePersonTjDTO> list = kq02Dao.getCalendarList(params);
        AttendancePersonTjDTO dto = kq02Dao.getPersonStatistics(params);
        object.put("list", list);
        object.put("arrivepercent", dto.getArrivepercent());
        object.put("kqs", dto.getKqs());
        return new Result().ok(object);
    }

    @Override
    public PageData<AttendanceArriveDTO> getManagerArrivePercenet(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<AttendanceArriveDTO> list = kq02Dao.getManagerArrivePercenet(params);
        return getPageData(list, page.getTotal(), AttendanceArriveDTO.class);
    }

    @Override
    public PageData<AttendanceTeamDTO> getTeamStatistics(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "kqworkers", false);
        List<AttendanceTeamDTO> list = kq02Dao.getTeamStatistics(params);
        return getPageData(list, page.getTotal(), AttendanceTeamDTO.class);
    }
}
