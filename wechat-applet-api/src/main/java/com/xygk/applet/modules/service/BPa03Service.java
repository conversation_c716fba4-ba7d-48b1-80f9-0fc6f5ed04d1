package com.xygk.applet.modules.service;

import com.xygk.applet.modules.dto.BPa03DTO;
import com.xygk.applet.modules.dto.WagesDto;
import com.xygk.applet.modules.entity.BPa03Entity;
import com.xygk.applet.supervision.dto.WagesInfoDto;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;

import java.util.Map;

/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-15
 */
public interface BPa03Service extends CrudService<BPa03Entity, BPa03DTO> {
    /**
     *
     * @description 通过项目ID获取个人工资记录
     * <AUTHOR>
     * @date 2021年10月18日 10:04
     */
    PageData<WagesDto> getWagesList(Map<String, Object> params);
    /**
     *
     * @description 获取工人工资信息（监管端）
     * <AUTHOR>
     * @date 2022年12月27日 15:45
     */
    PageData<WagesInfoDto> getWagesInfo(Map<String, Object> params);
}
