package com.xygk.applet.modules.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title WorkerInfoDto
 * @Description 工人信息Dto
 * @Date 2021/10/15 10:57
 * @Copyright 2019-2025
 */
@Data
@ApiModel(value = "工人信息")
public class WorkerInfoDto {
    @ApiModelProperty("工人ID")
    private Long ps0201;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("证件号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;
    @ApiModelProperty("性别")
    private String gender;
    @ApiModelProperty("头像路径")
    private String headimageurl;
    @ApiModelProperty("班组名称")
    private String teamname;
    @ApiModelProperty("进退场状态")
    private String inOrOut;
    @ApiModelProperty("进场时间")
    private Date entrytime;
    @ApiModelProperty("退场时间")
    private Date exittime;
    @ApiModelProperty("是否上传劳动合同（1：已上传，0：未上传）")
    private String isUploadContract;
}
