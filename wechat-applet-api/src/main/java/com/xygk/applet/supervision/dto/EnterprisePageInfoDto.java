package com.xygk.applet.supervision.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *  @title EnterprisePageInfoDto
 *  @Description 企业分页信息DTO
 *  <AUTHOR>
 *  @Date 2022/12/28 11:31
 *  @Copyright 2019-2025
 */
@Data
@ApiModel(value = "企业分页信息")
public class EnterprisePageInfoDto {
    @ApiModelProperty(value = "主键ID")
    private Long cp0101;
    @ApiModelProperty(value = "社会统一信用码")
    private String corpcode;
    @ApiModelProperty(value = "企业名称")
    private String corpname;
    @ApiModelProperty(value = "参建类型")
    private String corptype;
    @ApiModelProperty(value = "注册地区")
    private String areacode;
    @ApiModelProperty(value = "企业地址")
    private String address;
    @ApiModelProperty(value = "法定代表人")
    private String legalman;
    @ApiModelProperty(value = "注册资本（万元）")
    private BigDecimal regcapital;
    @ApiModelProperty(value = "经营状态")
    private String businessstatus;
    @ApiModelProperty(value = "参建项目数")
    private Integer projectcount;
    @ApiModelProperty(value = "联系人")
    private String linkman;
    @ApiModelProperty(value = "联系电话")
    private String linkcellphone;
}
