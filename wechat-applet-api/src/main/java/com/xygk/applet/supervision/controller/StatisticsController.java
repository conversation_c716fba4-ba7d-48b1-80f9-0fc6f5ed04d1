package com.xygk.applet.supervision.controller;

import com.xygk.applet.common.annotation.Login;
import com.xygk.applet.common.enums.RoleEnum;
import com.xygk.applet.modules.service.StatisticsService;
import com.xygk.applet.supervision.dto.*;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 统计分析
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("/api/supervision/statistics")
@Api(tags = "统计分析（监管端）")
public class StatisticsController {
    @Autowired
    private StatisticsService statisticsService;

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("area")
    @ApiOperation("区划统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areacode", value = "行政区划", paramType = "query", dataType = "String")
    })
    public Result<AreaStatisticsDTO> area(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.areaStatistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("worker")
    @ApiOperation("用工统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "statisticsdate", value = "统计日期（yyyy-MM-dd）", paramType = "query", dataType = "String")
    })
    public Result<WorkerStatisticsDTO> worker(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.workerStatistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("attendance1")
    @ApiOperation("在岗情况统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areacode", value = "主管部门", paramType = "query", dataType = "String")
    })
    public Result<PageData<Attendance1StatisticsDTO>> attendance1(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Attendance1StatisticsDTO> page = statisticsService.attendance1Statistics(userId, params);
        return new Result<PageData<Attendance1StatisticsDTO>>().ok(page);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("attendance2")
    @ApiOperation("考勤时长统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areacode", value = "主管部门", paramType = "query", dataType = "String")
    })
    public Result<PageData<Attendance2StatisticsDTO>> attendance2(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Attendance2StatisticsDTO> page = statisticsService.attendance2Statistics(userId, params);
        return new Result<PageData<Attendance2StatisticsDTO>>().ok(page);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("attendance3")
    @ApiOperation("劳务人员考勤统计")
    @ApiImplicitParams({
    })
    public Result<Attendance3StatisticsDTO> attendance3(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.attendance3Statistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("attendance4")
    @ApiOperation("岗位人员考勤统计")
    @ApiImplicitParams({
    })
    public Result<Attendance4StatisticsDTO> attendance4(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.attendance4Statistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("attendance5")
    @ApiOperation("项目考勤统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areacode", value = "主管部门", paramType = "query", dataType = "String")
    })
    public Result<PageData<Attendance5StatisticsDTO>> attendance5(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Attendance5StatisticsDTO> page = statisticsService.attendance5Statistics(userId, params);
        return new Result<PageData<Attendance5StatisticsDTO>>().ok(page);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("salary1")
    @ApiOperation("欠薪项目统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areacode", value = "主管部门", paramType = "query", dataType = "String")
    })
    public Result<PageData<Salary1StatisticsDTO>> salary1(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Salary1StatisticsDTO> page = statisticsService.salary1Statistics(userId, params);
        return new Result<PageData<Salary1StatisticsDTO>>().ok(page);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("salary2")
    @ApiOperation("发放情况统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "quarter", value = "季度", paramType = "query", dataType = "String")
    })
    public Result<Salary2StatisticsDTO> salary2(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.salary2Statistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("salary3")
    @ApiOperation("欠薪企业统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpname", value = "企业名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areacode", value = "主管部门", paramType = "query", dataType = "String")
    })
    public Result<PageData<Salary3StatisticsDTO>> salary3(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Salary3StatisticsDTO> page = statisticsService.salary3Statistics(userId, params);
        return new Result<PageData<Salary3StatisticsDTO>>().ok(page);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("complaint1")
    @ApiOperation("企业投诉统计")
    @ApiImplicitParams({
    })
    public Result<ComplaintStatisticsDTO> complaint1(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.complaint1Statistics(userId, params);
    }

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("complaint2")
    @ApiOperation("工人投诉统计")
    @ApiImplicitParams({
    })
    public Result<ComplaintStatisticsDTO> complaint2(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {

        return statisticsService.complaint2Statistics(userId, params);
    }
}