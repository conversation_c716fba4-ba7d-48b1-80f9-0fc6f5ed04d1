package com.xygk.applet.supervision.controller;

import com.xygk.applet.common.annotation.Login;
import com.xygk.applet.common.enums.RoleEnum;
import com.xygk.applet.modules.service.BPs02Service;
import com.xygk.applet.supervision.dto.WorkerInfoDetailsDto;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title AttendanceInfoController
 * @Description 工人查找相关接口（监管端）
 * @Date 2022/12/27 16:24
 * @Copyright 2019-2025
 */
@RestController
@RequestMapping("/api/supervision/workerQuery")
@Api(tags = "工人查找相关接口（监管端）")
public class WorkerQueryController {
    @Autowired
    private BPs02Service ps02Service;

    @Login(role = RoleEnum.SUPERVISION)
    @PostMapping("getWorkerQueryList")
    @ApiOperation("获取人脸查找列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pj0101", value = "项目ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "faceImage", value = "人脸图像", paramType = "query", dataType = "String")
    })
    public Result<List<WorkerInfoDetailsDto>> getWorkerQueryList(@ApiIgnore @RequestAttribute("userId") Long userId, @ApiIgnore @RequestBody Map<String, Object> params) {
        params.put("userId", userId);
        List<WorkerInfoDetailsDto> list = ps02Service.getWorkerQueryList(params);
        return new Result<List<WorkerInfoDetailsDto>>().ok(list);
    }
}
