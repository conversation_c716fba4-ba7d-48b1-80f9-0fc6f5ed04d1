package com.xygk.applet.supervision.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 区划统计
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "区划统计")
public class AreaStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "区划代码")
    private String areacode;
    @ApiModelProperty(value = "在建项目数")
    private String zjprojects;
    @ApiModelProperty(value = "有考勤项目数")
    private String kqprojects;
    @ApiModelProperty(value = "无考勤项目数")
    private String nokqprojects;
    @ApiModelProperty(value = "上班工人总数")
    private String kqworkers;
    @ApiModelProperty(value = "更新率")
    private String kqpercent;
}