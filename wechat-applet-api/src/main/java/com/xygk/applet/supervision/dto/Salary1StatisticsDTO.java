package com.xygk.applet.supervision.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 欠薪项目统计
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "欠薪项目统计")
public class Salary1StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "主键ID")
    private Long pj0101;
    @ApiModelProperty(value = "主管部门")
    private String areacode;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
    @ApiModelProperty(value = "开工日期")
    private String startdate;
    @ApiModelProperty(value = "项目地址")
    private String address;
    @ApiModelProperty(value = "联系人")
    private String linkman;
    @ApiModelProperty(value = "联系电话")
    private String linkphone;
    @ApiModelProperty(value = "拖欠人数")
    private String workers;
}