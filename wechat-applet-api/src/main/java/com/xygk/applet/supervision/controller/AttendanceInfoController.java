package com.xygk.applet.supervision.controller;

import com.xygk.applet.common.annotation.Login;
import com.xygk.applet.common.enums.RoleEnum;
import com.xygk.applet.modules.service.BKq02Service;
import com.xygk.applet.supervision.dto.AttendancePageInfoDto;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * <AUTHOR>
 * @title AttendanceInfoController
 * @Description 考勤信息相关接口（监管端）
 * @Date 2022/12/27 16:24
 * @Copyright 2019-2025
 */
@RestController
@RequestMapping("/api/supervision/attendanceInfo")
@Api(tags = "考勤信息相关接口（监管端）")
public class AttendanceInfoController {
    @Autowired
    private BKq02Service bKq02Service;

    @Login(role = RoleEnum.SUPERVISION)
    @GetMapping("getAttendanceInfoList")
    @ApiOperation("获取考勤信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "personName", value = "人员姓名", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "personType", value = "人员类型", paramType = "query", dataType = "String")
    })
    public Result<PageData<AttendancePageInfoDto>> getAttendanceInfoList(
            @ApiIgnore @RequestAttribute("userId") Long userId,
            @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("userId", userId);
        PageData<AttendancePageInfoDto> page = bKq02Service.getAttendancePageInfoList(params);
        return new Result<PageData<AttendancePageInfoDto>>().ok(page);
    }
}
