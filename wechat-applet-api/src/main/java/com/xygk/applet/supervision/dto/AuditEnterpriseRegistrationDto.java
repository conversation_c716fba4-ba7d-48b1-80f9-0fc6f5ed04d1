package com.xygk.applet.supervision.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  @title AuditEnterpriseRegistrationDto
 *  @Description 审核企业信息注册信息DTO
 *  <AUTHOR>
 *  @Date 2023/1/3 16:29
 *  @Copyright 2019-2025
 */
@Data
@ApiModel("审核企业信息注册信息")
public class AuditEnterpriseRegistrationDto {
    @ApiModelProperty("企业ID")
    private Long cp0101;
    @ApiModelProperty("审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty("审核结果")
    private String auditresult;
    private Long userId;
}
