<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.common.task.utils.zworker.dao.ZWorkerDao">

    <insert id="batchInsert">
        insert into z_worker
        (
        projectid,
        isenter,
        corpcode,
        corpname,
        teamsysno,
        teamname,
        workername,
        isteamleader,
        idcardtype,
        idcardnumber,
        age,
        gender,
        nation,
        address,
        headimage,
        politicstype,
        cultureleveltype,
        grantorg,
        worktype,
        nativeplace,
        mobile,
        hascontract,
        userid
        )
        select a.* from(
        <foreach collection="list" item="item" separator="union all">
            select
            #{item.projectid},
            #{item.isenter},
            #{item.corpCode},
            #{item.corpName},
            #{item.teamSysNo},
            #{item.teamName},
            #{item.workerName},
            #{item.isTeamLeader},
            #{item.IDCardType},
            #{item.IDCardNumber},
            #{item.age},
            #{item.gender},
            #{item.nation},
            #{item.address},
            #{item.headImage},
            #{item.politicsType},
            #{item.cultureLevelType},
            #{item.grantorg},
            #{item.workType},
            #{item.nativePlace},
            #{item.mobile},
            #{item.hasContract},
            #{item.userId}
            from dual
        </foreach>
        ) a
    </insert>

    <select id="selectWorkers" resultType="com.xygk.applet.common.task.utils.zworker.entity.ZWorkerEntity">
        select * from Z_WORKER t where t.ps0101 is null and length(t.idcardnumber) = 18 and rownum &lt; 2001
    </select>
    <select id="selectRepeatList" resultType="com.xygk.applet.common.task.utils.zworker.entity.ZWorkerEntity">
        select h.*
        from (select t.projectid, t.idcardnumber, count(1) as isenter
        from Z_WORKER t
        group by t.projectid, t.idcardnumber
        having count(1) > 1) h
        where rownum &lt; 1000
    </select>
    <delete id="deleteRepeat">
        delete from z_worker t
        where t.projectid = #{projectid}
        and t.idcardnumber = #{idcardnumber}
        and rownum &lt; #{isenter}
    </delete>
</mapper>