<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.config.sms.dao.SysSmsTaskDao">

    <select id="selectListForOrder" resultType="com.xygk.applet.config.sms.entity.SysSmsTaskEntity">
        select h.*
          from (select t.*
                  from SYS_SMS_TASK t
                 where t.status = '0'
                 order by t.create_date) h
         where rownum &lt; 101
    </select>
</mapper>