<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.common.task.utils.zsalary.dao.ZSalaryDao">

    <select id="getSalaryId" resultType="com.xygk.applet.common.task.utils.zsalary.entity.ZSalaryEntity">
        select *
        from (select * from z_salary where isconvert = '0')
        where rownum &lt;= 1
    </select>


</mapper>