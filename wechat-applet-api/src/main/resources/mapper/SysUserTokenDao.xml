<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.modules.dao.SysUserTokenDao">

    <select id="getByToken" resultType="com.xygk.applet.modules.entity.SysTokenEntity">
        select * from sys_user_token where token = #{value}
    </select>

    <select id="getByUserId" resultType="com.xygk.applet.modules.entity.SysTokenEntity">
        select * from sys_user_token where user_id = #{value}
    </select>

</mapper>