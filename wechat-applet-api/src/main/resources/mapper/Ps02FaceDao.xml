<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.modules.dao.Ps02FaceDao">

    <resultMap id="getFaceResultMap" type="com.xygk.applet.modules.entity.Ps02FaceEntity">
        <result property="ps0201" column="ps0201" />
        <result property="faceFeature" column="face_feature" />
    </resultMap>

    <select id="getFaceEntitiesByPj0101AreaCode" resultMap="getFaceResultMap">

        select t.ps0201, t.face_feature from b_ps02_face_data t
        <where>
            <if test="areacode != null and areacode != ''" >
             areacode = #{areacode}
            </if>
            <if test="projectId != null and projectId != ''" >
                and pj0101 = #{projectId}
            </if>
            <if test="inOrOut != null and inOrOut != ''" >
                and in_or_out = #{inOrOut}
            </if>
        </where>
    </select>

    <select id="getFaceDataEntitiesByPj0101AreaCode" resultType="com.xygk.applet.modules.entity.Ps02FaceDataInfoEntity">

        select t.ps0201, t.face_feature from b_ps02_face_data_info t
        <where>
            <if test="areacode != null and areacode != ''" >
                areacode = #{areacode}
            </if>
            <if test="projectId != null and projectId != ''" >
                and pj0101 = #{projectId}
            </if>
            <if test="inOrOut != null and inOrOut != ''" >
                and in_or_out = #{inOrOut}
            </if>
        </where>
    </select>
</mapper>