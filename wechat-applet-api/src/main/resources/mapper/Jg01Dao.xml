<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.modules.dao.Jg01Dao">

    <resultMap type="com.xygk.applet.modules.entity.Jg01Entity" id="jg01Map">
        <result property="jg0101" column="JG0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="warinfo" column="WARINFO"/>
        <result property="warstatus" column="WARSTATUS"/>
        <result property="opinions" column="OPINIONS"/>
        <result property="wartype" column="WARTYPE"/>
        <result property="starttime" column="STARTTIME"/>
        <result property="endtime" column="ENDTIME"/>
        <result property="processtatus" column="PROCESSTATUS"/>
        <result property="maxprocess" column="MAXPROCESS"/>
        <result property="relationrole" column="RELATIONROLE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getEarlyWarningPageList" resultType="com.xygk.applet.supervision.dto.EarlyWarningPageDto">
        select b.jg0101,
        a.name                                                       as projectName,
        (select d.warname from b_jg02 d where d.wartype = b.wartype) as warName,
        b.warstatus                                                  as warStatus,
        b.starttime                                                  as startTime,
        b.endtime                                                    as endTime,
        b.warinfo                                                    as warInfo,
        b.opinions
        from b_pj01 a,
        b_jg01 b,
        r_pj01_dept c
        where a.pj0101 = b.pj0101
        and a.pj0101 = c.pj0101
        and c.dept_id = #{deptId}
        and (b.processtatus = fun_get_userlevel(#{userId})
        or b.RELATIONROLE like '%' || #{roleId} || '%')
        <if test="projectName != '' and projectName != null">
            and a.NAME like '%' || #{projectName} || '%'
        </if>
        <if test="warType != '' and warType != null">
            and b.WARTYPE = #{warType}
        </if>
        <if test="warStatus != '' and warStatus != null">
            and b.WARSTATUS = #{warStatus}
        </if>
        order by b.WARSTATUS
    </select>
    <select id="selectRecordList" resultType="com.xygk.applet.supervision.dto.EarlyWarningRecordsDto">
        select a.JG0101,
               a.JG0501,
               a.DEALSTATUS                                               as dealStatus,
               a.DEALINFO                                                 as dealInfo,
               a.DEALLEVEL                                                as dealLevel,
               (select b.REAL_NAME from SYS_USER b where b.ID = a.DEALID) as dealUserName,
               a.DEALTIME                                                 as dealTime
        from B_JG05 a
        where a.JG0101 = #{id}
    </select>
    <select id="selectWarTypeList" resultType="com.xygk.applet.supervision.dto.EarlyWarningWarType">
        select a.WARNAME as label, a.WARTYPE as value
        from b_jg02 a
        where a.ISAVAILABLE = '1'
        order by a.SORT
    </select>
    <!--预警处置-->
    <select id="dispose" statementType="CALLABLE">
        {call PC_YJ_PROCESS(#{jg0101,mode=IN,jdbcType=VARCHAR},
                            #{dealStatus,mode=IN,jdbcType=VARCHAR},
                            #{dealInfo,mode=IN,jdbcType=VARCHAR},
                            #{dealId,mode=IN,jdbcType=VARCHAR},
                            #{result,mode=OUT ,jdbcType=VARCHAR})}
    </select>

    <select id="selectEarlyWarningInfo" resultType="com.xygk.applet.supervision.dto.EarlyWarning">

        select a.JG0101,
               (select d.warname from b_jg02 d where d.wartype = a.wartype) as warName,
               a.STARTTIME                                                  as startTime,
               a.ENDTIME                                                    as endTime,
               a.WARINFO                                                    as warInfo,
               a.OPINIONS
        from b_jg01 a
        where a.JG0101 = #{id}

    </select>
</mapper>
