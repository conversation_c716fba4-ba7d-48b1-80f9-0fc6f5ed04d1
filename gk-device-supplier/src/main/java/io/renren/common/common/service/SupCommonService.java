package io.renren.common.common.service;

import io.renren.common.common.dto.ProjectDTO;
import io.renren.common.common.dto.ProjectsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-06-12 12:58
 */
public interface SupCommonService {

    /**
     * 查询项目信息
     *
     * @return List<ProjectDTO>
     */
    List<ProjectDTO> getProjectInfo();


    /**
     * 查询项目LabelAndVlaue
     * @return
     */
    List<ProjectsDTO> getProjectInfos();
}
