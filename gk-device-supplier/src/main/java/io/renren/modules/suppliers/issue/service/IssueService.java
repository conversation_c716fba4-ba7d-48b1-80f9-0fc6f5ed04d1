package io.renren.modules.suppliers.issue.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.suppliers.issue.dto.AuthorDTO;
import io.renren.modules.suppliers.issue.dto.Pj01DTO;
import io.renren.modules.suppliers.issue.dto.RecordDTO;
import io.renren.modules.suppliers.supplier.dto.RegisterDTO;
import io.renren.modules.suppliers.supplier.entity.RegisterEntity;

import java.util.Map;

/**
 * 设备下发
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-10
 */
public interface IssueService extends CrudService<RegisterEntity, RegisterDTO> {

    PageData<Pj01DTO> projectPageList(Map<String, Object> params);

    Result devicePageList(Map<String, Object> params);

    Result personAuthor(AuthorDTO dto);

    PageData<RecordDTO> recordPageList(Map<String, Object> params);

    Result taskDelete(AuthorDTO dto);

    Result singlePersonAuthor(Map<String, Object> params);
}