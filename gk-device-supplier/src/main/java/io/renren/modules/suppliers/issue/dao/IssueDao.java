package io.renren.modules.suppliers.issue.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.suppliers.device.dto.DeviceDTO;
import io.renren.modules.suppliers.issue.dto.Pj01DTO;
import io.renren.modules.suppliers.issue.dto.RecordDTO;
import io.renren.modules.suppliers.supplier.entity.SupplierEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 设备供应商注册
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-10
 */
@Mapper
public interface IssueDao extends BaseDao<SupplierEntity> {


    List<Pj01DTO> projectPageList(Map<String, Object> params);

    List<DeviceDTO> devicePageList(Map<String, Object> params);

    List<RecordDTO> recordPageList(Map<String, Object> params);

    void taskDelete(String sn);
}