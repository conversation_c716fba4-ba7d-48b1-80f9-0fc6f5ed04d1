package io.renren.modules.suppliers.supplier.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 设备供应商注册
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SUP_REGISTER")
public class RegisterEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 供应商名称
     */
    private String name;
    /**
     * 统一社会信用代码
     */
    private String code;
    /**
     * 工商营业执照号
     */
    private String businessLicense;
    /**
     * 法定代表人
     */
    private String legalName;
    /**
     * 法人身份证号码
     */
    private String legalIdCardNumber;
    /**
     * 联系人
     */
    private String linkman;
    /**
     * 联系电话
     */
    private String linkmanPhone;
    /**
     * 供应设备类型
     */
    private String type;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    @JsonProperty(value = "passwords")
    private String password;
    /**
     * 审核状态
     */
    private String status;

    @ApiModelProperty(value = "供应商提供设备厂商名称", required = true)
    private String vendorname;
    /**
     * 注册时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date regTime;
    /**
     * 审核时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date confTime;

}