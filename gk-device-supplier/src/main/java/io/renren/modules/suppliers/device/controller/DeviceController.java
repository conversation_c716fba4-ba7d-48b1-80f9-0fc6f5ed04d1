package io.renren.modules.suppliers.device.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.suppliers.device.dto.DeviceDTO;
import io.renren.modules.suppliers.device.dto.DevicePageDTO;
import io.renren.modules.suppliers.device.service.DeviceService;
import io.renren.modules.suppliers.device.vo.DeviceVerifyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 考勤设备注册
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-11
 */
@RestController
@RequestMapping("supplier/device")
@Api(tags="考勤设备注册")
public class DeviceController {
    @Autowired
    private DeviceService deviceService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("supplier:device:page")
    public Result<PageData<DeviceDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<DeviceDTO> page = deviceService.page(params);

        return new Result<PageData<DeviceDTO>>().ok(page);
    }

    @GetMapping("pageList")
    @ApiOperation("设备信息分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "项目名称", paramType = "query", dataType="String")
    })
    @RequiresPermissions("supplier:device:pageList")
    public Result<PageData<DevicePageDTO>> pageList(@ApiIgnore @RequestParam Map<String, Object> params){

        return deviceService.pageList(params);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("supplier:device:info")
    public Result<DeviceDTO> get(@PathVariable("id") Long id){
        DeviceDTO data = deviceService.get(id);

        return new Result<DeviceDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("supplier:device:save")
    public Result save(@RequestBody DeviceDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        deviceService.saveInfo(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("supplier:device:update")
    public Result update(@RequestBody DeviceDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        deviceService.updateInfo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("enterprise:device:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        deviceService.delete(ids);

        return new Result();
    }


    @PostMapping("/verify")
    @ApiOperation("审核")
    @LogOperation("审核")
//    @RequiresPermissions("enterprise:device:verify")
    public Result verify(@RequestBody @Validated DeviceVerifyVo deviceVerifyVo){

        return deviceService.verify(deviceVerifyVo);
    }


    /**
     * 业务
     * 1供应商注册 监管端审核 通过之后写入部门表、用户表
     * 2通过之后绑定设备（供应商 选择项目 绑定设备，绑定成功生成公钥） 注: 保证设备序列号不重复。注意供应商不显示公钥私钥，项目端全部显示
     * 3项目审核（项目审核设备，通过之后生成私钥）
     */
}