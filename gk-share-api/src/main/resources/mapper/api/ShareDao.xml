<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ShareDao">
    <select id="getShareUser" resultType="io.renren.api.entity.ShareUserEntity">
        select t.*
          from I_SHARE_USER t
         where t.appkey = #{appkey}
           and t.whether = '1'
    </select>
    <select id="selectProjectByCode" resultType="java.lang.Long">
        select count(1) from b_pj01 t where t.pj0101 = #{projectcode}
    </select>
    <select id="checkUserAuth" resultType="java.lang.Long">
        select count(1)
          from i_share_project t
         where t.pj0101 = #{projectcode}
           and t.user_id = #{userid}
    </select>
    <select id="getCompanys" resultType="io.renren.api.dto.CompanyDTO">
        select t.*,a.*
          from b_cp02 t, b_cp01 a
         where t.cp0101 = a.cp0101
           and t.pj0101 = #{projectcode}
    </select>
    <select id="getTeams" resultType="io.renren.api.dto.TeamDTO">
        select b.corpcode, t.tm0101 as teamsysno, t.*
            from b_tm01 t, b_cp02 a, b_cp01 b
            where t.cp0201 = a.cp0201
            and a.cp0101 = b.cp0101
           and t.pj0101 = #{projectcode}
        <if test="corpcode !=null and corpcode !=''">
            and b.corpcode = #{corpcode}
        </if>
        <if test="inOrOut !=null and inOrOut !=''">
            and t.inOrOut = #{inOrOut}
        </if>
    </select>
    <select id="getManagers" resultType="io.renren.api.dto.ManagerDTO">
        select t.ps0401 as user_id,
                t.pj0101,
                t.cp0201,
                t.ps0301,
                t.jobtype,
                t.photo,
                t.entrytime,
                t.exittime,
                t.in_or_out,
                t.update_date,
                b.ps0101,
                b.name,
                b.idcardtype,
                b.idcardnumber,
                b.gender,
                b.nation,
                b.birthday,
                b.address,
                b.edulevel,
                b.degree,
                b.workertype,
                b.areacode,
                b.headimageurl,
                b.politicstype,
                b.isjoined,
                b.joinedtime,
                b.cellphone,
                b.cultureleveltype,
                b.specialty,
                b.hasbadmedicalhistory,
                b.urgentlinkman,
                b.urgentlinkmanphone,
                b.workdate,
                b.maritalstatus,
                b.grantorg,
                b.positiveidcardimageurl,
                b.negativeidcardimageurl,
                b.startdate,
                b.expirydate,d.corpcode
          from b_ps04 t, b_ps03 a, b_ps01 b, b_cp02 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and t.pj0101 = #{projectcode}
        <if test="corpcode !=null and corpcode !=''">
            and d.corpcode = #{corpcode}
        </if>
        <if test="inOrOut !=null and inOrOut !=''">
            and t.inOrOut = #{inOrOut}
        </if>
        <if test="updateDate !=null and updateDate !=''">
            and t.update_date &gt; to_date(#{updateDate}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
    </select>
    <select id="getWorkers" resultType="io.renren.api.dto.WorkerDTO">
        select  d.corpcode,
                b.tm0101 as teamsysno,
                t.isteamleader,
                t.worktypecode,
                t.issuecardpicurl,
                t.entrytime,
                t.exittime,
                t.in_or_out,
                t.update_date,
                a.ps0101,
                a.name,
                a.idcardtype,
                a.idcardnumber,
                a.gender,
                a.nation,
                a.birthday,
                a.address,
                a.edulevel,
                a.degree,
                a.workertype,
                a.areacode,
                a.headimageurl,
                a.politicstype,
                a.isjoined,
                a.joinedtime,
                a.cellphone,
                a.cultureleveltype,
                a.specialty,
                a.hasbadmedicalhistory,
                a.urgentlinkman,
                a.urgentlinkmanphone,
                a.workdate,
                a.maritalstatus,
                a.grantorg,
                a.positiveidcardimageurl,
                a.negativeidcardimageurl,
                a.startdate,
                a.expirydate,
                t.ps0201 as user_id
          from b_ps02 t, b_ps01 a, b_tm01 b, b_cp02 c, b_cp01 d
         where t.ps0101 = a.ps0101
           and t.tm0101 = b.tm0101
           and b.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and t.pj0101 = #{params.projectcode}
        <if test="params.corpcode !=null and params.corpcode !=''">
            and d.corpcode = #{params.corpcode}
        </if>
        <if test="params.teamsysno !=null and params.teamsysno !=''">
            and t.tm0101 = #{params.teamsysno}
        </if>
        <if test="params.inOrOut !=null and params.inOrOut !=''">
            and t.inOrOut = #{params.inOrOut}
        </if>
        <if test="params.updateDate !=null and params.updateDate !=''">
            and t.update_date &gt; to_date(#{params.updateDate}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        order by t.ps0201 desc
    </select>
    <select id="getAttendances" resultType="io.renren.api.dto.AttendanceDTO">
        select t.user_id,
               t.person_name,
               t.person_type,
               t.checkdate,
               t.direction,
               t.attendtype,
               t.image_url
          from B_KQ02 t
         where t.pj0101 = #{projectcode}
           and t.person_type is not null
           and t.checkdate &gt;= to_date(#{startdate}, 'yyyy-MM-dd hh24:mi:ss')
           and t.checkdate &lt;= to_date(#{enddate}, 'yyyy-MM-dd hh24:mi:ss')
        <if test="personType !=null and personType !=''">
            and t.person_type = #{personType}
        </if>
        <if test="userId !=null and userId !=''">
            and t.user_id = #{userId}
        </if>
    </select>
</mapper>