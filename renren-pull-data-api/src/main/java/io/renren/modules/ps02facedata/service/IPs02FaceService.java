package io.renren.modules.ps02facedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.common.utils.Result;
import io.renren.modules.ps02facedata.entity.Ps02FaceDataEntity;
import io.renren.modules.ps02facedata.vo.FaceAddParamsVO;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
public interface IPs02FaceService extends IService<Ps02FaceDataEntity> {

    /**
     * 人脸数据添加
     * @param faceAddParamsVO
     * @return
     */
    Result<String> faceFeatureAdd(FaceAddParamsVO faceAddParamsVO) ;

    /**
     * 采集人脸数据添加
     * @param base64
     * @return
     */
    byte[] collectFaceDataFeature(String base64) ;
}