package io.renren.job.annotation;


import java.lang.annotation.*;

/**
 *  @title GKScheduled
 *  @Description 系统定时任务注解
 *  <AUTHOR>
 *  @Date 2020/7/16 13:08
 *  @Copyright 2019-2025
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SyncGKScheduled {
    String scheduledName();
    String beanName();
    String params();
    String cronExpression();
    /**
     * 任务状态    0：失败    1：成功
     */
    int status();
}
