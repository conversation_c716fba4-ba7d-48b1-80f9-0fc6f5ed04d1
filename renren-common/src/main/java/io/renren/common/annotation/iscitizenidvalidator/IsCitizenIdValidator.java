package io.renren.common.annotation.iscitizenidvalidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 验证是否为身份证号码（18位中国）
 *
 * <AUTHOR>
 * @Date 2020-04-03
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.FIELD, ElementType.TYPE})
@Constraint(validatedBy = IsCitizenIdValidatorClass.class)
public @interface IsCitizenIdValidator {

    String message() default "身份证号码不正确！";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
