package io.renren.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入处理数据
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/4/18 9:44
 **/
@AllArgsConstructor
@Getter
public enum ImportDataDealTypeEnum {

    SUCCESS(1, "成功"),
    FAIL(0, "失败"),
    ;
    private final Integer type;
    private final String desc;

    private static Map<Integer, ImportDataDealTypeEnum> cache;

    static {
        cache = Arrays.stream(ImportDataDealTypeEnum.values()).collect(Collectors.toMap(ImportDataDealTypeEnum::getType, Function.identity()));
    }

    public static ImportDataDealTypeEnum getType(Integer type) {
        return cache.get(type);
    }
}
