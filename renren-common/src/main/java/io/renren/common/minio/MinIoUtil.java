package io.renren.common.minio;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import io.minio.MinioClient;
import io.minio.ObjectStat;
import io.minio.PutObjectOptions;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;

/**
 * minio文件工具类
 *
 * <AUTHOR>
 * @Date 2021-08-03 16:15
 */
@Component
public class MinIoUtil {

    private static MinioClient minioClient;

    private static MinIoClientConfig minIoClientConfig;

    public MinIoUtil(MinioClient minioClient, MinIoClientConfig minIoClientConfig) {
        MinIoUtil.minioClient = minioClient;
        MinIoUtil.minIoClientConfig = minIoClientConfig;
    }

    /**
     * 创建桶
     *
     * @param bucketName 桶名
     */
    @SneakyThrows
    public static void createBucket(String bucketName) {
        boolean isExist = minioClient.bucketExists(bucketName);
        if (!isExist) {
            minioClient.makeBucket(bucketName);
        }
    }

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @SneakyThrows
    public static String upload(MultipartFile file, String fileType) {
        String originalFilename = file.getOriginalFilename();
        if (!originalFilename.contains(".")) {
            //手机拍照没有文件后缀
            originalFilename = IdUtil.simpleUUID() + ".jpg";
        }
        Date date = DateUtil.date();
        String time = DateUtil.format(date, "yyyyMMdd");
        String year = String.valueOf(DateUtil.year(date));
        String fileName = null;
        if (originalFilename != null) {
            fileName = year + "/" + time + "/" + fileType + "/" + IdUtil.simpleUUID() + originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        PutObjectOptions putObjectOptions = new PutObjectOptions(file.getSize(), -1L);
        putObjectOptions.setContentType(file.getContentType());
        minioClient.putObject(minIoClientConfig.getBucketName(), fileName, file.getInputStream(), putObjectOptions);
        return minioClient.getObjectUrl(minIoClientConfig.getBucketName(), fileName);
    }


    /**
     * 文件上传
     *
     * @param file 文件
     * @return url
     */
    @SneakyThrows
    public static String uploadByMobile(MultipartFile file, String fileType) {
        String originalFilename = file.getOriginalFilename();
        Date date = DateUtil.date();
        String time = DateUtil.format(date, "yyyyMMdd");
        String year = String.valueOf(DateUtil.year(date));
        String fileName = null;
        // 判断文件是否存在后缀，如不存在后缀则定义
        if (originalFilename != null) {
            fileName = year + "/" + time + "/" + fileType + "/" + IdUtil.simpleUUID();
            String extension = FileUtil.extName(originalFilename);
            if (StringUtils.isBlank(extension)) {
                fileName = fileName + ".jpg";
            } else {
                fileName = fileName + originalFilename.substring(originalFilename.lastIndexOf("."));
            }
        }
        PutObjectOptions putObjectOptions = new PutObjectOptions(file.getSize(), -1L);
        putObjectOptions.setContentType(file.getContentType());
        minioClient.putObject(minIoClientConfig.getBucketName(), fileName, file.getInputStream(), putObjectOptions);
        return minioClient.getObjectUrl(minIoClientConfig.getBucketName(), fileName);
    }

    /**
     * 文件上传
     *
     * @param filePath 文件路径
     * @return url
     */
    @SneakyThrows
    public static String upload(String filePath) {
        Date date = DateUtil.date();
        String time = DateUtil.format(date, "yyyyMMdd");
        String year = String.valueOf(DateUtil.year(date));
        String fileName = year + "/" + time + "/" + IdUtil.simpleUUID() + "." + FileUtil.extName(filePath);
        minioClient.putObject(minIoClientConfig.getBucketName(), fileName, filePath, null);
        return minioClient.getObjectUrl(minIoClientConfig.getBucketName(), fileName);
    }

    /**
     * 文件上传
     *
     * @param stream: 文件流
     */
    @SneakyThrows
    public static String upload(InputStream stream, String fileType) {
        Date date = DateUtil.date();
        String time = DateUtil.format(date, "yyyyMMdd");
        String year = String.valueOf(DateUtil.year(date));
        String fileName = year + "/" + time + "/" + fileType + "/" + IdUtil.simpleUUID() + ".jpg";
        minioClient.putObject(minIoClientConfig.getBucketName(), fileName, stream, new PutObjectOptions(stream.available(), -1L));
        return minioClient.getObjectUrl(minIoClientConfig.getBucketName(), fileName);
    }

    /**
     * 文件上传(此方法目前只针对Base64转byte的图片)
     *
     * @param bytes: 文件字节
     */
    @SneakyThrows
    public static String upload(byte[] bytes, String fileType) {
        ByteArrayInputStream stream = new ByteArrayInputStream(bytes);
        Date date = DateUtil.date();
        String time = DateUtil.format(date, "yyyyMMdd");
        String year = String.valueOf(DateUtil.year(date));
        String fileName = year + "/" + time + "/" + fileType + "/" + IdUtil.simpleUUID() + ".jpg";
        PutObjectOptions putObjectOptions = new PutObjectOptions(stream.available(), -1L);
        putObjectOptions.setContentType("image/jpeg");
        minioClient.putObject(minIoClientConfig.getBucketName(), fileName, stream, putObjectOptions);
        return minioClient.getObjectUrl(minIoClientConfig.getBucketName(), fileName);
    }

    /**
     * 文件下载
     *
     * @param bucketName 桶名
     * @param fileName   文件名
     * @param response   响应数据
     */
    @SneakyThrows
    public static void download(String bucketName, String fileName, HttpServletResponse response) {
        // 获取对象的元数据
        final ObjectStat stat = minioClient.statObject(bucketName, fileName);
        response.setContentType(stat.contentType());
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        InputStream is = minioClient.getObject(bucketName, fileName);
        IOUtils.copy(is, response.getOutputStream());
        is.close();
    }

    /**
     * 删除文件
     *
     * @param bucketName 桶名
     * @param fileName   文件名
     */
    @SneakyThrows
    public static void deleteFile(String bucketName, String fileName) {
        minioClient.removeObject(bucketName, fileName);
    }
}
