#繁体中文
id.require=ID不能為空
id.null=ID必須為空
pid.require=上級ID，不能為空
sort.number=排序值不能小於0

sysparams.paramcode.require=參數編碼不能為空
sysparams.paramvalue.require=參數值不能為空

sysuser.username.require=用戶名不能為空
sysuser.password.require=密碼不能為空
sysuser.realname.require=姓名不能為空
sysuser.gender.range=性別取值範圍0~2
sysuser.email.require=郵箱不能為空
sysuser.email.error=郵箱格式不正確
sysuser.mobile.require=手機號不能為空
sysuser.deptId.require=部門不能為空
sysuser.superadmin.range=超級管理員取值範圍0~1
sysuser.status.range=狀態取值範圍0~1
sysuser.captcha.require=驗證碼不能為空
sysuser.uuid.require=唯一標識不能為空

sysmenu.pid.require=請選擇上級菜單
sysmenu.name.require=菜單名稱不能為空
sysmenu.type.range=菜單類型取值範圍0~1

sysdept.pid.require=請選擇上級部門
sysdept.name.require=部門名稱不能為空

sysrole.name.require=角色名稱不能為空

sysdict.type.require=字典類型不能為空
sysdict.name.require=字典名稱不能為空
sysdict.label.require=字典標籤不能為空

schedule.status.range=狀態取值範圍0~1
schedule.cron.require=cron表達式不能為空
schedule.bean.require=bean名稱不能為空

oss.type.range=類型取值範圍1~5

aliyun.accesskeyid.require=阿里雲AccessKeyId不能為空
aliyun.accesskeysecret.require=阿里雲AccessKeySecret不能為空
aliyun.signname.require=阿里雲短信簽名不能為空
aliyun.templatecode.require=阿里雲短信模板不能為空
aliyun.domain.require=阿里雲綁定的域名不能為空
aliyun.domain.url=阿里雲綁定的域名格式不正確
aliyun.endPoint.require=阿里雲EndPoint不能為空
aliyun.bucketname.require=阿里雲BucketName不能為空

qcloud.appid.require=騰訊雲AppId不能為空
qcloud.appkey.require=騰訊雲AppKey不能為空
qcloud.secretId.require=騰訊雲SecretId不能為空
qcloud.secretkey.require=騰訊雲SecretKey不能為空
qcloud.signname.require=騰訊雲短信簽名不能為空
qcloud.templateid.require=騰訊雲短信模板ID不能為空
qcloud.domain.require=騰訊雲綁定的域名不能為空
qcloud.domain.url=騰訊雲綁定的域名格式不正確
qcloud.bucketname.require=騰訊雲BucketName不能為空
qcloud.region.require=所屬地區不能為空

qiniu.domain.require=七牛綁定的域名不能為空
qiniu.domain.url=七牛綁定的域名格式不正確
qiniu.accesskey.require=七牛AccessKey不能為空
qiniu.secretkey.require=七牛SecretKey不能為空
qiniu.bucketname.require=七牛空間名不能為空
qiniu.templateId.require=七牛模板ID不能為空

fastdfs.domain.require=FastDFS綁定的域名不能為空
fastdfs.domain.url=FastDFS綁定的域名格式不正確

local.domain.require=本地上傳綁定的域名不能為空
local.domain.url=本地上傳綁定的域名格式不正確
local.path.url=存儲目錄不能為空

minio.endPoint.require=Minio EndPoint不能為空
minio.accesskey.require=Minio AccessKey不能為空
minio.secretkey.require=Minio SecretKey不能為空
minio.bucketname.require=Minio BucketName不能為空

sms.platform.range=平台類型取值範圍1~2

email.smtp.require=SMTP不能為空
email.port.require=端口號不能為空
email.username.require=郵箱賬號不能為空
email.password.require=郵箱密碼不能為空

mail.name.require=模板名稱不能為空
mail.subject.require=郵件主題不能為空
mail.content.require=郵件正文不能為空

model.name.require=模型名稱不能為空
model.key.require=模型標識不能為空

news.title.require=標題不能為空
news.content.require=內容不能為空
news.pubdate.require=發佈時間不能為空

region.id.require=區域標識不能為空
region.pid.require=上級區域不能為空
region.name.require=區域名稱不能為空

processBizRoute.procDefId.require=流程定義ID不能為空
processBizRoute.bizRoute.require=業務路由不能為空
processBizRoute.procDefKey.require=流程定義KEY不能為空
processBizRoute.version.require=流程定義版本號不能為空

ProcessStart.processDefinitionKey.require=流程定義KEY不能為空
ProcessStart.businessKey.require=業務KEY不能為空