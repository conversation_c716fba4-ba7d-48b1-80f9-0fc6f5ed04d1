package io.renren.modules.ot01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "附件查询")
public class ParamsInfo implements Serializable {
    private static final long serialVersionUID = -8182784944283650509L;

    @ApiModelProperty(value = "业务ID")
    @NotNull(message = "业务ID不能为空")
    private Long businessId;

    @ApiModelProperty(value = "附件类型")
    @NotBlank(message = "附件类型不能为空")
    private String fileType;
}
