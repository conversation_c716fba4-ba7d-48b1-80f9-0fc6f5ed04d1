package io.renren.modules.supdevicetaskdetail.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;

/**
 * 设备待办任务明细信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-17
 */
@Data
@ApiModel(value = "设备待办任务明细信息")
public class SupDeviceTaskDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "任务表ID(sup_device_task表的主键ID)")
	private BigDecimal taskId;
		@ApiModelProperty(value = "人员ID(ps0201或ps0401)")
	private BigDecimal userId;
		@ApiModelProperty(value = "人员类型(1工人，2管理人员)")
	private Short userType;
		@ApiModelProperty(value = "人员姓名")
	private String name;
		@ApiModelProperty(value = "人员照片(当sup_device_task的数据类型为1时，此字段内容可以为空)")
	private String photo;

}