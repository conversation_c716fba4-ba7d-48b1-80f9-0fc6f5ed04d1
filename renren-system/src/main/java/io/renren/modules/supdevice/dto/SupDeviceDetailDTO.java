package io.renren.modules.supdevice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 考勤设备注册
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-17
 */
@Data
@ApiModel(value = "考勤设备注册")
public class SupDeviceDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "供应商名称")
    private String suppliername;
    @ApiModelProperty(value = "设备序列号")
    private String sn;
    @ApiModelProperty(value = "设备公钥")
    private String appKey;
    @ApiModelProperty(value = "设备私钥")
    private String appSecret;
    @ApiModelProperty(value = "是否可用(0否，1是)")
    private String whether;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String status;

}