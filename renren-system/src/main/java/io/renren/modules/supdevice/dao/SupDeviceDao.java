package io.renren.modules.supdevice.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.supdevice.dto.SupDeviceDetailDTO;
import io.renren.modules.supdevice.dto.SupDevicePageDTO;
import io.renren.modules.supdevice.dto.SupDeviceUpdateNameDTO;
import io.renren.modules.supdevice.entity.SupDeviceEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 考勤设备注册
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-17
 */
@Mapper
public interface SupDeviceDao extends BaseDao<SupDeviceEntity> {


    /**
     * 分页
     * @param params
     * @return
     */
    List<SupDevicePageDTO> getPageList(Map<String, Object> params);


    /**
     * 获取详情数据
     * @param deviceId
     * @return
     */
    SupDeviceDetailDTO getInfo(Long deviceId) ;

    /**
     * 更新设备名称
     * @param dto
     */
    void updateDeviceName(SupDeviceUpdateNameDTO dto);
}