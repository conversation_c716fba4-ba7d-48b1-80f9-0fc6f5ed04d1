/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.admin.demo.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.admin.demo.dto.NewsDTO;
import io.renren.modules.admin.demo.entity.NewsEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 新闻
 *
 * <AUTHOR>
 */
@Mapper
public interface NewsDao extends BaseDao<NewsEntity> {

    List<NewsEntity> getList(Map<String, Object> params);

    /**
     * 获取标题的数据
     * @return
     */
    List<NewsDTO> selectTitleList();
}
