/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.modules.admin.sys.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.handler.LogInToDecryptHandler;
import io.renren.common.page.PageData;
import io.renren.common.password.PasswordUtils;
import io.renren.common.redis.RedisKeys;
import io.renren.common.redis.RedisUtils;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.Result;
import io.renren.common.utils.SmUtilUtils;
import io.renren.modules.admin.security.dto.LoginDTO;
import io.renren.modules.admin.security.dto.LoginRsaEncryptDTO;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.security.user.UserDetail;
import io.renren.modules.admin.sys.dao.SysDeptDao;
import io.renren.modules.admin.sys.dao.SysUserDao;
import io.renren.modules.admin.sys.dto.ForgetPasswordDto;
import io.renren.modules.admin.sys.dto.SysCheckPasswordDTO;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.entity.SysUserEntity;
import io.renren.modules.admin.sys.enums.SuperAdminEnum;
import io.renren.modules.admin.sys.service.SysDeptService;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.admin.sys.service.SysRoleUserService;
import io.renren.modules.admin.sys.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 系统用户
 *
 * @<NAME_EMAIL>
 */
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    @Autowired
    private SysRoleUserService sysRoleUserService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SysDeptDao deptDao;
    @Autowired
    private LogInToDecryptHandler<LoginDTO> logInToDecryptHandler;

    @Override
    public PageData<SysUserDTO> page(Map<String, Object> params) {
        //转换成like
        paramsToLike(params, "username");

        //分页
        IPage<SysUserEntity> page = getPage(params, Constant.CREATE_DATE, false);

        //普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        List<SysUserEntity> list = null;
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            String areacode = SecurityUser.getDeptAreaCode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
            list = baseDao.getListForRegul(params);
        } else {
            list = baseDao.getList(params);
        }
        //查询

        return getPageData(list, page.getTotal(), SysUserDTO.class);
    }

    @Override
    public PageData<SysUserDTO> pageList(Map<String, Object> params) {
        //分页
        IPage<SysUserEntity> page = getPage(params, Constant.CREATE_DATE, false);
        IPage<SysUserEntity> list = null;
        String areacode = SecurityUser.getDeptAreaCode();
        String lastcode = areacode.substring(4);
        if (Constant.CITY_LASECODE.equals(lastcode)) {
            params.put("areacode", areacode.substring(0, 4));
        } else {
            params.put("areacode", areacode);
        }
        String userType = params.get("userType").toString();
        if ("1".equals(userType)) {
            list = baseDao.getListForRegulPage(page, params);
        } else if ("2".equals(userType)) {
            list = baseDao.getListForPorjectPage(page, params);
        } else if ("3".equals(userType)) {
            list = baseDao.getListForPersonPage(page, params);
        } else if ("5".equals(userType)) {
            list = baseDao.getListForBankPage(page, params);
        } else if ("6".equals(userType)) {
            list = baseDao.getListForSupplierPage(page, params);
        }
        return getPageData(list, SysUserDTO.class);
    }

    @Override
    public List<SysUserDTO> list(Map<String, Object> params) {
        //普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            params.put("deptIdList", sysDeptService.getSubDeptIdList(user.getDeptId()));
        }

        List<SysUserEntity> entityList = baseDao.getList(params);

        return ConvertUtils.sourceToTarget(entityList, SysUserDTO.class);
    }

    @Override
    public SysUserDTO get(Long id) {
        SysUserEntity entity = baseDao.getById(id);
        SysUserDTO sysUserDTO = ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
        return sysUserDTO;
    }

    @Override
    public SysUserDTO dealProvinceCode(SysUserDTO data) {
        //判断用户类型，若为项目用户，返回住建厅code
        if ("1".equals(data.getUserType())) {
            if (StringUtils.isNotBlank(CommonUtils.userProjectInfo().getCode())) {
                data.setCode(SmUtilUtils.encrypt(CommonUtils.userProjectInfo().getCode()));
            }
        }
        return data;
    }

    @Override
    public SysUserDTO getByUsername(String username) {
        SysUserEntity entity = baseDao.getByUsername(username);
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO dto) {
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        //密码加密
        String password = PasswordUtils.encode(entity.getPassword());
        entity.setPassword(password);

        //保存用户
        entity.setSuperAdmin(SuperAdminEnum.NO.value());
        String userType = SecurityUser.getUser().getUserType();
        if (Constant.MANAG_USER.equals(userType)) {
//            SysDeptEntity dept = deptDao.selectById(dto.getDeptId());
//            if (!dept.getName().contains("住建局")) {
//                throw new RenException("所属部门请选择【监管】类型");
//            }
            String userType1 = dto.getUserType();
            if (!Constant.MANAG_USER.equals(userType1)) {
                throw new RenException("用户类型请选择【监管用户】");
            }
            List<Long> roleIds = dto.getRoleIdList();
            if (roleIds.size() > 1) {
                throw new RenException("请勿选择多个角色");
            }
//            if (!roleIds.contains(1392741351649132545L)) {
//                throw new RenException("请选择【监管角色】");
//            }
        }

        insert(entity);

        //保存角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserDTO dto) {
        String userType = SecurityUser.getUser().getUserType();
        if (Constant.MANAG_USER.equals(userType)) {
            SysDeptEntity dept = deptDao.selectById(dto.getDeptId());
            if (!dept.getName().contains("住建局")) {
                throw new RenException("所属部门请选择【监管】类型");
            }
            String userType1 = dto.getUserType();
            if (!Constant.MANAG_USER.equals(userType1)) {
                throw new RenException("用户类型请选择【监管用户】");
            }
            List<Long> roleIds = dto.getRoleIdList();
            if (roleIds.size() > 1) {
                throw new RenException("请勿选择多个角色");
            }
            if (!roleIds.contains(1392741351649132545L)) {
                throw new RenException("请选择【监管角色】");
            }
        }
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        //密码加密
        if (StringUtils.isBlank(dto.getPassword())) {
            entity.setPassword(null);
        } else {
            String password = PasswordUtils.encode(entity.getPassword());
            entity.setPassword(password);
        }

        //更新用户
        updateById(entity);

        //更新角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());
    }

    @Override
    public void delete(Long[] ids) {
        //删除用户
        baseDao.deleteBatchIds(Arrays.asList(ids));

        //删除角色用户关系
        sysRoleUserService.deleteByUserIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String newPassword) {
        // 判断当前密码是否是强密码，如果不是则不通过
        if (!checkPasswordStrong(newPassword)) {
            throw new RenException("当前密码不符合密码策略，请重新修改！");
        }

        newPassword = PasswordUtils.encode(newPassword);

        baseDao.updatePassword(id, newPassword);
    }

    @Override
    public int getCountByDeptId(Long deptId) {
        return baseDao.getCountByDeptId(deptId);
    }

    @Override
    public List<Long> getUserIdListByDeptId(List<Long> deptIdList) {
        return baseDao.getUserIdListByDeptId(deptIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(List<Long> asList) {
        List<SysUserEntity> sysUserEntities = baseDao.selectBatchIds(asList);
        //查询密码重置设置的密码
        String password = sysParamsService.getValue(Constant.PASSWORD);
        sysUserEntities.forEach(sysUserEntity -> {
            //设置初始密码
            sysUserEntity.setPassword(PasswordUtils.encode(password));
            baseDao.updateById(sysUserEntity);
        });

    }

    @Override
    public void forgetPassword(ForgetPasswordDto dto) {
        String verifyCode = null;
        // 校验验证码
        if ("1".equals(dto.getUsertype())) {
            verifyCode = (String) redisUtils.get(RedisKeys.getProjectRegisterKey(dto.getLinkcellphone()));
        } else {
            verifyCode = (String) redisUtils.get(RedisKeys.getRegisterKey(dto.getLinkcellphone()));
        }
        if (StrUtil.isBlank(verifyCode)) {
            throw new RenException("验证码失效");
        }
        if (!verifyCode.equals(dto.getSmsCode())) {
            throw new RenException("验证码错误");
        }
        //校验确认密码
        if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
            throw new RenException("新密码两次输入不一致！");
        }

        // 判断当前密码是否是强密码，如果不是则不通过
        if (!checkPasswordStrong(dto.getNewPassword())) {
            throw new RenException("当前密码不符合密码策略，请重新修改！");
        }

        SysUserEntity user = null;
        if ("2".equals(dto.getUsertype())) {
            user = baseDao.getCompanyByCellphone(dto.getLinkcellphone());
        } else {
            user = baseDao.getProjectByCellphone(dto.getLinkcellphone());
        }
        if (user == null) {
            throw new RenException("该手机号未查询到相关用户！");
        }
        //修改密码
        user.setPassword(PasswordUtils.encode(dto.getNewPassword()));
        baseDao.updateById(user);
    }

    /**
     * 检查账号强弱密码：0弱密码，1强密码
     *
     * @return
     */
    @Override
    public Result checkPassword(LoginRsaEncryptDTO sysCheckPasswordDTO) {
        LoginDTO checkPasswordDTO = logInToDecryptHandler.decrypt(sysCheckPasswordDTO.getAuth(), LoginDTO.class);
        String password = checkPasswordDTO.getPassword();
        if (StringUtils.isEmpty(password)) {
            return new Result().ok("0");
        }
        return checkPasswordStrong(password) ? new Result().ok("1") : new Result().ok("0");
    }

    /**
     * 判断当前密码是否是强密码
     *
     * @return
     */
    private static boolean checkPasswordStrong(String password) {
//        String regx = "^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{8,}$";
        String regx = "^(?![A-Za-z]+$)(?![A-Z\\d]+$)(?![A-Z\\W]+$)(?![a-z\\d]+$)(?![a-z\\W]+$)(?![\\d\\W]+$)[^\\u4e00-\\u9fa5]{8,16}$";
        return password.matches(regx);
    }
}
