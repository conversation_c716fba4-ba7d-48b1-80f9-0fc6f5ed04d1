package io.renren.common.common.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.common.dto.Cp01DTO;
import io.renren.common.common.dto.Ps01DTO;
import io.renren.common.common.service.CommonService;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 常用的方法
 * <p>
 * 主要用于页面展示的常用方法
 * 如：页面选择参建单位、选择班组等
 * </P>
 *
 * <AUTHOR>
 * @Date 2020-06-12 12:55
 */
@RestController
@RequestMapping("common")
@Api(tags = "系统管理通用方法")
public class CommonController {
    @Autowired
    private CommonService commonService;

    /**
     * 查询当前登录用户下边的参建单位
     * <p>
     * 应用场景:
     * 前端页面办理业务或者页面展示的时候,需要选择参建单位的下拉框
     * </p>
     *
     * @return list
     */
    @GetMapping("getPartUnitsInfo")
    @ApiOperation("参建单位信息")
    public Result<List<CommonDto>> getPartUnitsInfo() {
        List<CommonDto> partUnitsList = commonService.getPartUnitsList();
        return new Result<List<CommonDto>>().ok(partUnitsList);
    }

    /**
     * 查询当前登录用户下边的班组
     * <p>
     * 应用场景:
     * 前端页面办理业务或者页面展示的时候,需要选择班组的下拉框
     * </p>
     *
     * @return list
     */
    @GetMapping("getTeamInfo")
    @ApiOperation("班组信息")
    public Result<List<CommonDto>> getTeamInfo() {
        List<CommonDto> teamList = commonService.getTeamList();
        return new Result<List<CommonDto>>().ok(teamList);
    }

    /**
     * 项目上报配置获取上报地信息
     * <p>
     * 应用场景:
     * 前端页面办理业务或者页面展示的时候,需要选择上报地的下拉框
     * </p>
     *
     * @return list
     */
    @GetMapping("getReportInfo")
    @ApiOperation("获取上报地信息")
    public Result<List<CommonDto>> getReportInfo() {
        List<CommonDto> list = commonService.getReportInfo();
        return new Result<List<CommonDto>>().ok(list);
    }

    /**
     * 根据社会统一代码查询企业信息
     * <p>
     * 应用场景：
     * 新增参建单位的时候，当输入的统一社会信用代码存在的时候，直接返回信息到页面；
     * 或者其它地方需要用到参建单位基础的地方
     * </P>
     *
     * @param corpCode 社会统一信用代码
     * @return Cp01DTO
     */
    @GetMapping("getParticipantUnits/{corpCode}")
    @ApiOperation("参建单位基础信息")
    public Result<Cp01DTO> queryParticipantUnits(@PathVariable("corpCode") String corpCode) {

        Cp01DTO data = commonService.getParticipantUnitsInfo(corpCode);
        return new Result<Cp01DTO>().ok(data);
    }

    /**
     * 根据身份证号码查询人员基础信息
     * <p>
     * 应用场景：
     * 新增工人的时候，当人员存在的时候，会直接返回人员基础信息或者其它需要人员基础信息的地方
     * </p>
     *
     * @param idCardNumber 身份证号码
     * @return Ps01DTO
     */
    @GetMapping("getPersonnel/{idCardNumber}")
    @ApiOperation("人员基础信息")
    public Result<Ps01DTO> queryPersonnelInfo(@PathVariable("idCardNumber") String idCardNumber) {

        Ps01DTO data = commonService.getPersonnelInfo(idCardNumber);
        return new Result<Ps01DTO>().ok(data);
    }

    /**
     * 查询当前用户下行政区划代码
     *
     * @return
     */
    @GetMapping("selectAreaByDept")
    @ApiOperation("查询当前用户下行政区划代码")
    @LogOperation("查询当前用户下行政区划代码")
    public Result<List<CommonDto>> selectAreaByDept() {
        List<CommonDto> data = commonService.selectAreaByDept();
        return new Result<List<CommonDto>>().ok(data);
    }


}
