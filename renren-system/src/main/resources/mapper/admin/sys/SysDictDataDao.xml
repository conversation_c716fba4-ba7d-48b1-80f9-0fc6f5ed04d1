<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.sys.dao.SysDictDataDao">

    <select id="getDictDataList" resultType="io.renren.modules.admin.sys.entity.DictData">
        select dict_type_id, dict_label, dict_value from sys_dict_data where status = '1' order by dict_type_id, sort
    </select>

    <select id="getJobType" resultType="io.renren.modules.admin.sys.entity.DictData">
        select t1.jobtype dictValue, t1.jobtypename dictLabel
        from SYS_JOBTYPE t1
        where t1.corptype = #{corpType}
    </select>

    <select id="getTeamInfo" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select TM0101 value, TEAMNAME label from B_TM01 t
    </select>

    <select id="getUnemployeedManagers" resultType="io.renren.modules.admin.sys.entity.DictData">
        select t1.ps0301 dictValue, t2.name dictLabel
        from b_ps03 t1, b_ps01 t2
        where t1.ps0101 = t2.ps0101
          and t1.managestatus = '1'
          and t1.cp0101 = #{cp0101}
          and t1.ps0301 not in (select distinct t1.ps0301
                                from  b_ps04 t4
                                where t1.ps0301 = t4.ps0301
                                  and t4.in_or_out = '1')

    </select>

    <select id="getProjects" resultType="io.renren.modules.admin.sys.entity.DictData">
        select t.name dictLabel,
               t.pj0101 dictValue
        from B_PJ01 t, B_CP02 a, B_CP01 b
        where t.pj0101 = a.pj0101
          and a.cp0101 = b.cp0101
          and b.cp0101 = #{userCp0101}

    </select>

    <select id="getTeamCreditList" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select teamname label, tm0101 value from b_tm01 where pj0101 = #{pj0101}
    </select>

    <select id="getWorkerCreditList" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t1.name label, t1.ps0101 value from b_ps01 t1, b_ps03 t2 where t1.ps0101 = t2.ps0101 and t2.pj0101 = #{pj0101}
    </select>
    <select id="getNationByLabel" resultType="java.lang.String">
        select t.dict_value
          from SYS_DICT_DATA t
         where t.dict_type_id = 1
           and t.dict_label = #{nation}
    </select>
    <select id="selectListByDictType" resultType="io.renren.modules.admin.sys.entity.DictData">
        select x.*
          from sys_dict_data x, sys_dict_type y
         where x.dict_type_id = y.id
           and y.dict_type = #{dicttype}
    </select>

    <select id="getByDictTypeAndValue" resultType="io.renren.modules.admin.sys.dto.SysDictDataDTO">
        select x.*
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
          and y.dict_type = #{dictType}
          and x.dict_value = #{value}
    </select>
</mapper>