<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.common.common.dao.CommonDao">
    <!--通用的查询项目下边的参建单位信息SQL-->
    <select id="loadCp02Info" resultType="io.renren.common.common.dto.CommonDto">
        SELECT a.CP0201                                                       value,
               b.CORPNAME || ' (' || (SELECT c.DICT_LABEL
                                      FROM SYS_DICT_TYPE d,
                                           SYS_DICT_DATA c
                                      WHERE d.ID = c.DICT_TYPE_ID
                                        AND d.DICT_TYPE = 'CORPTYPE'
                                        AND c.DICT_VALUE = a.CORPTYPE) || ')' label
        FROM B_CP02 a,
             b_cp01 b,
             R_PJ01_DEPT t
        WHERE a.PJ0101 = t.PJ0101
          AND a.CP0101 = b.CP0101
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="loadTm01Info" resultType="io.renren.common.common.dto.CommonDto">
        select a.TM0101 value, a.TEAMNAME label
        from B_TM01 a,
             R_PJ01_DEPT t
        where a.PJ0101 = t.PJ0101
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="selectReportParamList" resultType="io.renren.common.common.dto.CommonDto">
        select a.ID value, a.REPORT_AREA_CODE label
        from B_PJ01_REPORT_PARAMS a
    </select>

    <select id="loadCp01" resultType="io.renren.common.common.dto.Cp01DTO">
        select *
        from B_CP01 t
        where t.CORPCODE = #{corpCode}
    </select>

    <select id="loadUserProjectInfo" resultType="io.renren.common.common.dto.Pj01DTO">
        select *
        from B_PJ01 t
        where t.DEPT_ID = #{deptId}
    </select>

    <select id="selectByIdCardNumber" resultType="io.renren.common.common.dto.Ps01DTO">
        select *
        from B_PS01 t
        where t.IDCARDNUMBER = #{idCardNumber}
    </select>

    <select id="getSupplierId" resultType="java.lang.Long">
        SELECT t.id
        FROM sup_supplier t
        where t.dept_id = #{deptId}
    </select>
    <!--预警处置-->
    <select id="dispose" statementType="CALLABLE">
        {call PC_YJ_PROCESS(#{jg0101,mode=IN,jdbcType=VARCHAR},
                            #{dealStatus,mode=IN,jdbcType=VARCHAR},
                            #{dealInfo,mode=IN,jdbcType=VARCHAR},
                            #{dealId,mode=IN,jdbcType=VARCHAR},
                            #{result,mode=OUT ,jdbcType=VARCHAR})}
    </select>
    <!--预警类型下拉框数据查询-->
    <select id="selectWarTypeList" resultType="io.renren.common.common.dto.EarlyWarningWarType">
        select a.WARNAME as label, a.WARTYPE as value
        from b_jg02 a
        where a.ISAVAILABLE = '1'
        order by a.SORT
    </select>
</mapper>