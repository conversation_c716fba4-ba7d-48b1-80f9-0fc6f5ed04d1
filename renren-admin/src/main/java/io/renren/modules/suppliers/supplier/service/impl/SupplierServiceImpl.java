package io.renren.modules.suppliers.supplier.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.suppliers.supplier.dao.SupplierDao;
import io.renren.modules.suppliers.supplier.dto.SupplierDTO;
import io.renren.modules.suppliers.supplier.entity.SupplierEntity;
import io.renren.modules.suppliers.supplier.service.SupplierService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 设备供应商
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplierServiceImpl extends CrudServiceImpl<SupplierDao, SupplierEntity, SupplierDTO> implements SupplierService {

    @Override
    public QueryWrapper<SupplierEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public Result publicList(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        List<SupplierDTO> list = baseDao.publicList(params);
        return result.ok(list);
    }
}