package io.renren.modules.suppliers.visitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chris
 * @Date : 2022-11-18
 **/
@Data
public class VisitorDTO {

    @ApiModelProperty(value = "访客ID")
    private Long ps1001;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "头像")
    private String headimageurl;
    @ApiModelProperty(value = "联系电话")
    private String linkphone;
    @ApiModelProperty(value = "访客类型")
    private String visitortype;
    @ApiModelProperty(value = "机构ID")
    private Long deptId;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "授权人")
    private String accreditor;
    @ApiModelProperty(value = "授权状态")
    private String accreditstatus;
    @ApiModelProperty(value = "授权时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "授权项目")
    private Long projects;
}
