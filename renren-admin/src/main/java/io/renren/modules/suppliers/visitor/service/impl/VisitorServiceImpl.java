package io.renren.modules.suppliers.visitor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constants.CommonConstants;
import io.renren.common.file.ImageUtil;
import io.renren.common.minio.MinIoUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import io.renren.modules.suppliers.visitor.dao.Kq02VisitorDao;
import io.renren.modules.suppliers.visitor.dao.Ps10Dao;
import io.renren.modules.suppliers.visitor.dao.Ps10Pj01Dao;
import io.renren.modules.suppliers.visitor.dto.Kq02VisitorDTO;
import io.renren.modules.suppliers.visitor.dto.VisitorDTO;
import io.renren.modules.suppliers.visitor.entity.Ps10Entity;
import io.renren.modules.suppliers.visitor.entity.Ps10Pj01Entity;
import io.renren.modules.suppliers.visitor.service.VisitorService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 设备供应商注册
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VisitorServiceImpl extends CrudServiceImpl<Ps10Dao, Ps10Entity, VisitorDTO> implements VisitorService {
    @Autowired
    private SupDeviceTaskService taskService;
    @Autowired
    private Ps10Pj01Dao ps10Pj01Dao;
    @Autowired
    private Kq02VisitorDao kq02VisitorDao;

    @Override
    public QueryWrapper<Ps10Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Ps10Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public PageData<VisitorDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<Ps10Entity> page = getPage(params, "", false);
        List<VisitorDTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), VisitorDTO.class);
    }

    @Override
    public PageData<Kq02VisitorDTO> attendancePage(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps10Entity> page = getPage(params, "CHECKDATE", false);
        List<Kq02VisitorDTO> list = kq02VisitorDao.attendancePage(params);
        return getPageData(list, page.getTotal(), Kq02VisitorDTO.class);
    }

    @Override
    public Result saveInfo(VisitorDTO dto) {
        Result<Object> result = new Result<>();
        Long deptId = SecurityUser.getDeptId();
        Integer count = baseDao.selectCount(new QueryWrapper<Ps10Entity>().eq("NAME", dto.getName()).eq("LINKPHONE", dto.getLinkphone()).eq("VISITORTYPE", "2").eq("DEPT_ID", deptId));
        if (count > 0) {
            return result.error("该人员已存在，请勿重复添加！");
        }
        byte[] bytes = HttpUtil.downloadBytes(dto.getHeadimageurl());
        if (bytes.length > 100 * 1024) {
            bytes = ImageUtil.compressPicCycle(bytes, 100 * 1024, 0.9, false);
            dto.setHeadimageurl(MinIoUtil.upload(bytes, "normal"));
        }
        Ps10Entity ps10 = new Ps10Entity();
        BeanUtil.copyProperties(dto, ps10, CopyOptions.create().setIgnoreNullValue(true));
        ps10.setDeptId(deptId);
        baseDao.insert(ps10);
        return result;
    }

    @Override
    public void deviceAddPerson(Long ps1001, short authorType) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        List<PersonDTO> list = baseDao.selectAuthList(ps1001);
        if (authorType == CommonConstants.PERSON_INTO) {
            //查询人员是否已经授权
            Integer authorCount = ps10Pj01Dao.selectCount(new QueryWrapper<Ps10Pj01Entity>().eq("PS1001", ps1001).eq("PJ0101", pj0101));
            if (authorCount == 0) {
                Ps10Pj01Entity ps10Pj01 = new Ps10Pj01Entity();
                ps10Pj01.setPs1001(ps1001);
                ps10Pj01.setPj0101(pj0101);
                ps10Pj01Dao.insert(ps10Pj01);
            }
            taskService.personIntoDevicesByPj0101(list, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, pj0101);
        } else if (authorType == CommonConstants.PERSON_DELETE) {
            taskService.personIntoDevicesByPj0101(list, CommonConstants.PERSON_DELETE, CommonConstants.MANAGER_TYPE, pj0101);
            ps10Pj01Dao.delete(new QueryWrapper<Ps10Pj01Entity>().eq("PS1001", ps1001).eq("PJ0101", pj0101));
        }
    }
}