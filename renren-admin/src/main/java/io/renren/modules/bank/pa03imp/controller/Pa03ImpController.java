package io.renren.modules.bank.pa03imp.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.bank.pa03imp.dto.Pa03ImpDTO;
import io.renren.modules.bank.pa03imp.service.Pa03ImpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-26
 */
@RestController
@RequestMapping("bank/pa03imp")
@Api(tags = "工资代发明细导入")
public class Pa03ImpController {
    @Autowired
    private Pa03ImpService pa03ImpService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("bank:pa03imp:page")
    public Result<PageData<Pa03ImpDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Pa03ImpDTO> page = pa03ImpService.pageList(params);

        return new Result<PageData<Pa03ImpDTO>>().ok(page);
    }

    @PostMapping("/importData")
    @ApiOperation("数据导入")
    @RequiresPermissions("bank:pa03imp:importData")
    public Result importData(@RequestParam("file") MultipartFile file) throws Exception {
        Result result = pa03ImpService.importData(file);
        return result;
    }

    @GetMapping("/download")
    @ApiOperation("导入模板下载")
    public void download(HttpServletResponse response) {
        pa03ImpService.download(response);
    }
}