package io.renren.modules.bank.pa02imp.dao;


import io.renren.common.dao.BaseDao;
import io.renren.modules.bank.pa02imp.dto.Pa02ImpDTO;
import io.renren.modules.bank.pa02imp.entity.Pa02ImpEntity;
import io.renren.modules.bank.pa03imp.dto.Pa03ImpDTO;
import io.renren.modules.enterprise.pa02.entity.Pa02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
@Mapper
public interface Pa02ImpDao extends BaseDao<Pa02ImpEntity> {

    List<Pa02ImpDTO> page(Map<String, Object> params);

    List<Pa02ImpDTO> getPageList(Map<String, Object> params);

}
