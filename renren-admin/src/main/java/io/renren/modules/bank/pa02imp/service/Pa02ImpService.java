package io.renren.modules.bank.pa02imp.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.bank.pa02imp.dto.Pa02ImpDTO;
import io.renren.modules.bank.pa02imp.entity.Pa02ImpEntity;
import io.renren.modules.bank.pa03imp.dto.Pa03ImpDTO;
import io.renren.modules.enterprise.pa02.dto.Pa02DTO;
import io.renren.modules.enterprise.pa02.entity.Pa02Entity;
import io.renren.modules.enterprise.pa03.dto.Pa03DTO;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
*<AUTHOR>
*@date 2021/10/9
*/

public interface Pa02ImpService extends CrudService<Pa02ImpEntity, Pa02ImpDTO> {

    PageData<Pa02ImpDTO> pageList(Map<String, Object> params);

    void download(HttpServletResponse response);

    Result importData(MultipartFile file) throws Exception;
}
