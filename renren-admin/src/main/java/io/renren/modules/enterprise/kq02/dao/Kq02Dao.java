package io.renren.modules.enterprise.kq02.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.dao.BaseDao;
import io.renren.common.page.PageData;
import io.renren.modules.enterprise.kq02.dto.*;
import io.renren.modules.enterprise.kq02.entity.Kq02Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Kq02Dao extends BaseDao<Kq02Entity> {
    /**
     * 查询考勤数据
     * @param params
     * @return
     */
    List<Kq02PageDTO> getListData(Map<String, Object> params);

    Integer selectByCheckDate(Kq02DTO dto);

    List<TjAttendanceDTO> exportPage(Map<String, Object> params);

    void generate(Map<String, Object> paramMap);

    /**
     * 连续无考勤天数
     *
     * @param params
     * @return
     */
    IPage<Kq02NoAttendanceStatisticalDTO> getNoAbsenceDays(@Param("page") IPage page,  @Param("params") Map<String, Object> params);

    /**
     * 获取当前项目下发失败人员名单
     *
     * @param page
     * @param params
     * @return
     */
    IPage<Kq02GetFailedIssuedDTO> getFailedIssued(@Param("page") IPage page, @Param("params") Map<String, Object> params);

    /**
     *获取当前项目下发失败人员名单具体详情
     *
     * @param params
     * @return
     */
    List<Kq02GetFailedIssuedDetailDTO> getFailedIssuedDetail(Map<String, Object> params);

    List<CommonDto> getDeviceList(Long pj0101);
}