package io.renren.modules.enterprise.cg06.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* 通用配置阶段实体
*
* @<NAME_EMAIL>
* @since 3.0 2025-05-22
*/
@Data
@ApiModel(value = "通用配置阶段实体")
public class GeneralStageDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "项目id")
    private Long[] pj0101s;
    @ApiModelProperty(value = "通用配置类型（0为配置1-5阶段，1为仅配置6阶段，2为仅配置7阶段）")
    private String type;



}