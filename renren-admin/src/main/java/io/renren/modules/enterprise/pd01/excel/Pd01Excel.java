package io.renren.modules.enterprise.pd01.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 施工现场机械基本信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
public class Pd01Excel {
    @Excel(name = "主键ID")
    private BigDecimal pd0101;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "环保信息标签编号")
    private String tagno;
    @Excel(name = "机械型号")
    private String model;
    @Excel(name = "机械名称")
    private String devicename;
    @Excel(name = "商标")
    private String trademark;
    @Excel(name = "机械类型")
    private String devicetype;
    @Excel(name = "排放阶段")
    private String dischargestage;
    @Excel(name = "制造厂商")
    private String manufacturer;
    @Excel(name = "发动机型号")
    private String enginetype;
    @Excel(name = "出厂年月")
    private Date factorymonthly;
    @Excel(name = "产权单位")
    private String propertyunit;
    @Excel(name = "产权单位统一社会信用代码")
    private String creditcode;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}