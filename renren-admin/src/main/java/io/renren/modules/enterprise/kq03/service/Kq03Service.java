package io.renren.modules.enterprise.kq03.service;


import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.kq03.dto.Kq03DTO;
import io.renren.modules.enterprise.kq03.entity.Kq03Entity;

import javax.validation.constraints.NotBlank;

/**
 * 设备注册码
 *
 * <AUTHOR>
 * @since 1.0.0 2020-05-25
 */
public interface Kq03Service extends CrudService<Kq03Entity, Kq03DTO> {
    /**
     * 生成设备注册码，保存
     */
    void saveDeviceCode();

    /**
     * 验证设备注册码是否可用
     *
     * @param deviceKey 设备注册码
     */
    void validatorDeviceKey(String deviceKey);

    /**
     * 保存设备序列号和设备注册码的关系
     *
     * @param devicesNo 设备序列号
     * @param deviceKey 设备注册码
     */
    void saveDeviceKeyAndDevicesNo(@NotBlank String devicesNo, @NotBlank String deviceKey);
}