package io.renren.modules.enterprise.cg06.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* 区域档案上传配置
*
* @<NAME_EMAIL>
* @since 3.0 2025-05-22
*/
@Data
@ApiModel(value = "档案上传配置")
public class Cg06DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "阶段id")
    private List<Cg06FileNumsDTO> list;



}