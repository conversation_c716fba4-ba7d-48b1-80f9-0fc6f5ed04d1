package io.renren.modules.enterprise.pd02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.pd02.dto.Pd02ConfirmImportDataDTO;
import io.renren.modules.enterprise.pd02.dto.Pd02DTO;
import io.renren.modules.enterprise.pd02.dto.Pd02HandleAbnormalDTO;
import io.renren.modules.enterprise.pd02.entity.Pd02Entity;

import java.util.Map;

/**
 * 工人身份证导入数据记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-04-17
 */
public interface Pd02Service extends CrudService<Pd02Entity, Pd02DTO> {

    /**
     * 分页
     *
     * @param params
     * @return
     */
    PageData<Pd02DTO> pageList(Map<String, Object> params);

    /**
     * 处理异常数据
     *
     * @return
     */
    Result<String> handleAbnormalData(Pd02HandleAbnormalDTO pd02HandleAbnormalDTO);

    /**
     * 确认导入银行卡数据
     *
     * @param pd02ConfirmImportDataDTO
     * @return
     */
    Result<String> confirmImportData(Pd02ConfirmImportDataDTO pd02ConfirmImportDataDTO);
}