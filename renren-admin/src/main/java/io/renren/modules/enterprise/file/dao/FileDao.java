package io.renren.modules.enterprise.file.dao;

import io.renren.modules.enterprise.file.dto.EmpRecordFormDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020-11-11 10:54
 */
@Mapper
public interface FileDao {
    /**
     * 查询用工备案信息
     * @param params
     * @return
     */
    List<EmpRecordFormDTO> selectEmpRecordFormList(Map<String, Object> params);
}
