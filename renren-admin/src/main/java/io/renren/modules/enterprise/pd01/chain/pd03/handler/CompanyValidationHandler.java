package io.renren.modules.enterprise.pd01.chain.pd03.handler;

import io.renren.common.common.util.StringNumberedAppendBuilder;
import io.renren.modules.enterprise.pd01.chain.ValidationHandler;
import io.renren.modules.enterprise.pd01.chain.pd03.dto.Pd03ProcessParams;
import io.renren.modules.enterprise.pd03.dto.Pd03DTO;
import io.renren.modules.enterprise.pd03.dto.Pd03PersonCompanyDTO;

import java.util.Map;

/**
 * 企业校验
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/5/24 16:03
 **/
public class CompanyValidationHandler extends ValidationHandler {

    @Override
    protected boolean doHandle(Object obj) {
        boolean isValid = true;
        Pd03ProcessParams params = (Pd03ProcessParams) obj;
        Map<String, Pd03PersonCompanyDTO> pd03PersonCompanyDTOMap = params.getPd03PersonCompanyDTOMap();
        StringNumberedAppendBuilder numberedStringBuilder = params.getNumberedStringBuilder();
        Pd03DTO pd03DTO = params.getPd03DTO();

        Pd03PersonCompanyDTO pd03PersonCompanyDTO = pd03PersonCompanyDTOMap.get(pd03DTO.getIdcardnumber());
        if (pd03PersonCompanyDTO == null) {
            numberedStringBuilder.append("未找到当前项目中人员在此企业的工作信息");
            isValid = false;
        } else {
            pd03DTO.setCorpcode(pd03PersonCompanyDTO.getCorpcode());
            pd03DTO.setCorpname(pd03PersonCompanyDTO.getCorpname());
            pd03DTO.setLinkcellphone(pd03PersonCompanyDTO.getLinkcellphone());
            pd03DTO.setLegalman(pd03PersonCompanyDTO.getLegalman());
            pd03DTO.setCorpaddress(pd03PersonCompanyDTO.getCorpaddress());
        }
        return isValid;
    }
}
