package io.renren.modules.enterprise.kq01.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.kq01.dto.DeviceCopyDTO;
import io.renren.modules.enterprise.kq01.dto.DeviceDTO;
import io.renren.modules.enterprise.kq01.dto.Kq01DTO;
import io.renren.modules.enterprise.kq01.dto.Kq01PageDTO;
import io.renren.modules.enterprise.kq01.entity.Kq01Entity;
import io.renren.modules.enterprise.kq01.vo.DeviceVerifyVo;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Kq01Service extends CrudService<Kq01Entity, Kq01DTO> {
    /**
     * 保存考勤设备信息
     * @param dto
     */
    void saveKq01Info(Kq01DTO dto);

    /**
     * 查询列表数据
     * @param params
     * @return
     */
    PageData<Kq01PageDTO> pageList(Map<String, Object> params);

    /**
     * 修改
     * @param dto
     */
    void updateKq01Info(Kq01DTO dto);

    /**
     * 删除
     * @param ids
     */
    void deleteKq01Info(Long[] ids);

    /**
     * 校验设备序列号是否存在
     * @param devicemotion 设备序列号
     */
    void checkDevices(@NotNull String devicemotion);

    /**
     * 查询当前项目下边的设备信息
     * @param pj0101 项目ID
     * @return
     */
    List<Kq01DTO> selectByPj0101(Long pj0101);

    /**
     * 查询待复制的设备
     * @param params
     * @return list
     */
    PageData<DeviceDTO> getDevList(Map<String, Object> params);

    /**
     * 人员设备复制保存
     * @param dto
     */
    void saveDeviceCopy(DeviceCopyDTO dto);


    /**
     *
     * @param deviceVerifyVo
     * @return
     */
    Result verify(DeviceVerifyVo deviceVerifyVo) ;
}