package io.renren.modules.enterprise.cp02.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constants.CommonConstants;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.dto.Cp01DTO;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.cp01.service.Cp01Service;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.dto.Cp02DTO;
import io.renren.modules.enterprise.cp02.dto.Cp02ItemsDTO;
import io.renren.modules.enterprise.cp02.dto.Cp02PageDTO;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.cp02.service.Cp02Service;
import io.renren.modules.enterprise.cp02.vo.Cp02SaveItemsVO;
import io.renren.modules.enterprise.cp02.vo.Cp02SaveVO;
import io.renren.modules.enterprise.cp02audit.dao.Cp02AuditDao;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDTO;
import io.renren.modules.enterprise.cp02audit.entity.Cp02AuditEntity;
import io.renren.modules.enterprise.cp02audit.service.Cp02AuditService;
import io.renren.modules.enterprise.cp03.dao.BCp03Dao;
import io.renren.modules.enterprise.cp03.entity.BCp03Entity;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Cp02ServiceImpl extends CrudServiceImpl<Cp02Dao, Cp02Entity, Cp02DTO> implements Cp02Service {
    @Autowired
    private Cp01Service cp01Service;
    @Autowired
    private Cp02Service cp02Service;
    @Autowired
    private Cp02AuditService cp02AuditService;
    @Autowired
    private Cp02AuditDao cp02AuditDao;
    @Autowired
    private BCp03Dao cp03Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    /**
     * 建设单位
     */
    private static final String CONSTRUCTION_UNIT = "8";
    /**
     * 总包单位
     */
    private static final String GENERAL_CONTRACTOR = "9";

    /**
     * 设计单位
     */
    private static final String DESIGN_UNIT = "11";

    /**
     * 勘探单位
     */
    private static final String PROSPECT_UNIT = "10";

    @Override
    public QueryWrapper<Cp02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Cp02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public Cp02SaveVO getInfo(Long id) {
        Cp02Entity cp02Entity = baseDao.selectById(id);
        Cp02SaveVO cp02DTO = ConvertUtils.sourceToTarget(cp02Entity, Cp02SaveVO.class);
        //查询参建单位的基础信息
        Cp01Entity cp01Entity = cp01Service.getById(cp02Entity.getCp0101());
        Cp01DTO cp01DTO = ConvertUtils.sourceToTarget(cp01Entity, Cp01DTO.class);
        BeanUtil.copyProperties(cp01DTO, cp02DTO, CopyOptions.create().setIgnoreNullValue(true));
        return cp02DTO;
    }

    @Override
    public PageData<Cp02PageDTO> pageList(Map<String, Object> params) {
        paramsToLike(params, "corpname");
        // 分页
        IPage<Cp02Entity> page = getPage(params, "", false);
        // 查询
        params.put("deptId", SecurityUser.getDeptId());
        List<Cp02PageDTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Cp02PageDTO.class);
    }

    @Override
    public void saveCp02Info(Cp02DTO dto) {
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setPj0101(pj0101);
        //TODO 除了分包单位，其他单位只能存在一个
        if (dto.getCorptype().equals(GENERAL_CONTRACTOR)) {
            //校验总包单位,同一个项目下边只能存在一个总包单位
            cp02Service.checkParticipantsUnits(pj0101, GENERAL_CONTRACTOR, "项目下边只能存在一个总包单位");
        }
        if (dto.getCorptype().equals(CONSTRUCTION_UNIT)) {
            //校验建设单位,同一个项目下边只能存在一个建设单位
            cp02Service.checkParticipantsUnits(pj0101, CONSTRUCTION_UNIT, "项目下边只能存在一个建设单位");
        }
        //新增或者更新CP01
        Cp01Entity cp01Entity = ConvertUtils.sourceToTarget(dto.getCp01DTO(), Cp01Entity.class);
        cp01Service.saveOrUpdate(cp01Entity);
        //保存CP02信息
        dto.setCp0101(cp01Entity.getCp0101());
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        cp02Service.save(dto);
        //保存参建单位的进场记录
        BCp03Entity cp03Entity = new BCp03Entity();
        cp03Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setCp0201(dto.getCp0201()).setInOrOut("1");
        cp03Dao.insert(cp03Entity);

        //调用发送数据服务用于上报数据
    }

    @Override
    public void saveCp02Info(Cp02SaveVO dto) {

        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();

        // 核验当前项目下除了分包只有一个参见类型
        //checkCompanyOnlyOneByType(pj0101, dto.getCp0201(), dto.getCorptype());

        // 审核只能有一个
        Integer cp02WaitCount = cp02AuditDao.selectCount(new QueryWrapper<Cp02AuditEntity>()
                .eq("pj0101", pj0101)
                .eq("cp0101", dto.getCp0101())
                .eq("auditstatus", CommonConstants.COMPANY_WAIT_PASS)
        );
        if (cp02WaitCount > 0) {
            throw new RenException("当前单位已提交申请,请勿重复提交");
        }

        Cp02AuditDTO cp02DTO = new Cp02AuditDTO();
        cp02DTO.setCp0101(dto.getCp0101());
        cp02DTO.setEntrytime(dto.getEntrytime());
        cp02DTO.setPj0101(pj0101);
        cp02DTO.setInOrOut("1");
        cp02AuditService.save(cp02DTO);

        //保存参建单位的进场记录
        BCp03Entity cp03Entity = new BCp03Entity();
        cp03Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setCp0201(dto.getCp0201()).setInOrOut("1");
        cp03Dao.insert(cp03Entity);

    }

    @Override
    public void checkParticipantsUnits(Long pj0101, String corpType, String message) {
        Integer participantsUnitsCount = baseDao.getParticipantsUnitsCount(pj0101, corpType);
        if (participantsUnitsCount > 0) {
            throw new RenException(message);
        }
    }

    @Override
    public void updateCp02Info(Cp02DTO dto) {
        //当参建类型发生变化的时候需要校验以下数据
        Cp02DTO cp02DTO = cp02Service.get(dto.getCp0201());
        String corpType = cp02DTO.getCorptype();
        if (!dto.getCorptype().equals(corpType)) {
            //校验总包单位,同一个项目下边只能存在一个总包单位
            if (dto.getCorptype().equals(GENERAL_CONTRACTOR)) {
                cp02Service.checkParticipantsUnits(dto.getPj0101(), GENERAL_CONTRACTOR, "项目下边只能存在一个总包单位");
            }
            //校验建设单位,同一个项目下边只能存在一个建设单位
            if (dto.getCorptype().equals(CONSTRUCTION_UNIT)) {
                cp02Service.checkParticipantsUnits(dto.getPj0101(), CONSTRUCTION_UNIT, "项目下边只能存在一个建设单位");
            }
        }
        //新增或者更新CP01
//        Cp01Entity cp01Entity = ConvertUtils.sourceToTarget(dto.getCp01DTO(), Cp01Entity.class);
//        cp01Service.saveOrUpdate(cp01Entity);
        //更新CP02信息
        dto.setCp0101(dto.getCp0101());
        cp02Service.update(dto);
        //调用发送数据服务用于上报数据
    }

    @Override
    public Result updateCp02Info(Cp02SaveVO dto) {
        Result<Object> result = new Result<>();
        // 上报省厅之后将不能修改
//        Pj01Entity pj01 = pj01Dao.selectById(dto.getPj0101());
//        if ("1".equals(pj01.getStupstatus())) {
//            return result.error("项目信息已上报住建厅，不允许修改！");
//        }
        Cp02Entity cp02 = baseDao.selectById(dto.getCp0201());
        // 校验企业的参建类型是否发生修改
        if (!cp02.getCorptype().equals(dto.getCorptype())) {
            checkCompanyOnlyOneByType(dto.getPj0101(), dto.getCorptype());
        }
        //校验企业主体是否发生修改
        if (!cp02.getCp0101().equals(dto.getCp0101())) {
            checkOnlyOne(dto.getPj0101(), dto.getCorpcode());
        }
        Cp01Entity cp01 = cp01Dao.selectOne(new QueryWrapper<Cp01Entity>().eq("CORPCODE", dto.getCorpcode()));
        BeanUtil.copyProperties(dto, cp01, CopyOptions.create().setIgnoreNullValue(true));
        cp01Dao.updateById(cp01);
        //当参建类型发生变化的时候需要校验以下数据
        BeanUtil.copyProperties(dto, cp02, CopyOptions.create().setIgnoreNullValue(true));
        baseDao.updateById(cp02);
        return result;
    }

    @Override
    public void addCompanyInfo(Cp02SaveVO dto) {
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        // 核验当前项目下五方主体只能各存在一个
        checkCompanyOnlyOneByType(pj0101, dto.getCorptype());
        //校验当前添加企业只能在项目中参建一次
        checkOnlyOne(pj0101, dto.getCorpcode());
        //查询企业是否在系统中存在
        Cp01Entity cp01 = cp01Dao.selectOne(new QueryWrapper<Cp01Entity>().eq("CORPCODE", dto.getCorpcode()));
        BeanUtil.copyProperties(dto, cp01, CopyOptions.create().setIgnoreNullValue(true));
        cp01Dao.updateById(cp01);
        //添加参建单位信息
        Cp02Entity cp02 = new Cp02Entity();
        cp02.setCp0101(cp01.getCp0101());
        cp02.setPj0101(pj0101);
        cp02.setInOrOut("1");
        BeanUtil.copyProperties(dto, cp02, CopyOptions.create().setIgnoreNullValue(true));
        baseDao.insert(cp02);
    }


    @Override
    public Result<List<Cp02ItemsDTO>> getCp02Items() {

        return new Result<List<Cp02ItemsDTO>>()
                .ok(baseDao.getCp02Items(CommonUtils.userProjectInfo().getPj0101()));
    }

    @Override
    public Result<List<Cp02ItemsDTO>> getSubPackageCompanyItems() {

        return new Result<List<Cp02ItemsDTO>>()
                .ok(baseDao.getSubPackageCompanyItems(CommonUtils.userProjectInfo().getPj0101()));
    }

    /**
     * 校验当前企业下除了分包单位只能有一个企业
     *
     * @param pj0101
     * @param type
     */
    private void checkCompanyOnlyOneByType(Long pj0101, String type) {
        Long companyCount = baseDao.checkCompanyOnlyOneByType(pj0101, type, CollectionUtil.toList(CommonConstants.FIVESUBJECTCOMPANY));
        if (companyCount > 0) {
            throw new RenException(String.format("已存在参建类型为【%s】的企业,请勿重复添加！", baseDao.corpTypeName(type)));
        }
    }

    /**
     * 核验一个企业只能同一项目中参建一次
     */
    private void checkOnlyOne(Long pj0101, String corpcode) {
        Cp01Entity cp01 = cp01Dao.selectOne(new QueryWrapper<Cp01Entity>().eq("CORPCODE", corpcode));
        if (cp01 != null) {
            Integer one = baseDao.selectCount(new QueryWrapper<Cp02Entity>()
                    .eq("pj0101", pj0101)
                    .eq("cp0101", cp01.getCp0101())
            );
            if (one > 0) {
                throw new RenException("添加企业已参建当前项目，请勿重复添加！");
            }
        }
    }


    /**
     * 手动校验cp01数据
     */
    private void checkCompanyBaseInfo(Cp02SaveItemsVO cp02SaveItemsVO) {

        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<Cp02SaveItemsVO>> set = validator.validate(cp02SaveItemsVO);
        for (ConstraintViolation<Cp02SaveItemsVO> constraintViolation : set) {
            throw new RenException(constraintViolation.getMessage());
        }
    }
}