package io.renren.modules.enterprise.kq09.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考勤配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_KQ09")
public class Kq09Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long kq0901;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 连续无考勤天数
     */
    private Long noAttendanceDays;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}