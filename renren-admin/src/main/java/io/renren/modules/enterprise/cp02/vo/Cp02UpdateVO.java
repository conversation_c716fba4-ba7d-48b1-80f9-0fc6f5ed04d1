package io.renren.modules.enterprise.cp02.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
@ApiModel(value = "参建单位信息")
public class Cp02UpdateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "cp0201")
    private Long cp0201;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "请选择一个企业")
    private Long cp0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "参建类型_Select选择器", required = true)
    @NotBlank(message = "参建类型不能为空")
    private String corptype;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

}