package io.renren.modules.enterprise.cg06.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.modules.enterprise.cg05.dto.Cg05DisplayDTO;
import io.renren.modules.enterprise.cg06.dto.*;
import io.renren.modules.enterprise.cg06.service.Cg06Service;
import io.renren.modules.enterprise.pj01info.dto.Ot01DTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 项目档案阶段配置表
 *
 * <AUTHOR> <EMAIL>
 * @since 3.0 2025-05-22
 */
@RestController
@RequestMapping("enterprise/bcg06")
@Api(tags = "项目档案阶段配置表")
public class Cg06Controller {

    @Autowired
    private Cg06Service cg06Service;

    @GetMapping("page")
    @ApiOperation("获取项目各阶段档案分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:bcg06:page")
    public Result<PageData<Cg06DTO>> pageList(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Cg06DTO> page = cg06Service.pageList(params);
        return new Result<PageData<Cg06DTO>>().ok(page);
    }

    @GetMapping("getList")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:bcg06:getList")
    public Result<List<TransferDTO>> getList(Long pj0101) {
        List<TransferDTO> datas = cg06Service.getList(pj0101);
        return new Result<List<TransferDTO>>().ok(datas);
    }

    @GetMapping("getFileTree")
    @ApiOperation("获取档案树")
    @RequiresPermissions("enterprise:bcg06:getFileTree")
    public Result<List<FileTreeDTO>> getFileTree() {
        List<FileTreeDTO> list = cg06Service.getFileTree();
        return new Result<List<FileTreeDTO>>().ok(list);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:bcg06:save")
    public Result save(@RequestBody Cg06ConfigurationDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        cg06Service.saveData(dto);
        return new Result();
    }

    @GetMapping("getSpecifyFilesList")
    @ApiOperation("获取指定阶段类型附件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cg0801", value = "上传表ID", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:bcg06:getSpecifyFilesList")
    public Result<List<Ot01DTO>> getSpecifyFilesList(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<Ot01DTO> datas = cg06Service.getSpecifyFilesList(params);
        return new Result<List<Ot01DTO>>().ok(datas);
    }

    @PostMapping("saveFile")
    @ApiOperation("上传文件保存")
    @LogOperation("上传文件保存")
    @RequiresPermissions("enterprise:bcg06:saveFile")
    public Result saveFile(@RequestBody Cg06FilesSaveDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        cg06Service.saveFile(dto);
        return new Result();
    }


    @GetMapping("getFilesList")
    @ApiOperation("获取附件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cg0601", value = "项目档案阶段配置表id", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "ssny", value = "所属年月", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:bcg06:getFilesList")
    public Result<List<Cg05DisplayDTO>> getFilesList(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<Cg05DisplayDTO> datas = cg06Service.getFilesList(params);
        return new Result<List<Cg05DisplayDTO>>().ok(datas);
    }

    @GetMapping("getMonthFilesList")
    @ApiOperation("获取按月类型附件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cg0701", value = "项目档案阶段附件配置表id", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "year", value = "所属年", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("cg06:bcg06:getMonthFilesList")
    public Result<List<Cg05DisplayDTO>> getMonthFilesList(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<Cg05DisplayDTO> datas = cg06Service.getMonthFilesList(params);
        return new Result<List<Cg05DisplayDTO>>().ok(datas);
    }

    @PostMapping("saveGeneralStage")
    @ApiOperation("保存通用化的阶段配置")
    @RequiresPermissions("enterprise:bcg06:saveGeneralStage")
    public Result saveGeneralStage(@RequestBody GeneralStageDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Result result = cg06Service.saveGeneralStage(dto);
        return result;
    }

    @GetMapping("getIsUpdate")
    @ApiOperation("获取是否允许修改附件标志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cg0601", value = "项目阶段配置id", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:bcg06:getIsUpdate")
    public Result<StageIsUpdateDTO> getIsUpdate(@ApiIgnore @RequestParam Map<String, Object> params) {
        StageIsUpdateDTO dto = cg06Service.getIsUpdate(params);
        return new Result<StageIsUpdateDTO>().ok(dto);
    }

    @PostMapping("saveIsUpdate")
    @ApiOperation("保存是否允许修改附件标志")
    @RequiresPermissions("enterprise:bcg06:saveIsUpdate")
    public Result saveIsUpdate(@RequestBody StageIsUpdateDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        Result result = cg06Service.saveIsUpdate(dto);
        return result;
    }


}