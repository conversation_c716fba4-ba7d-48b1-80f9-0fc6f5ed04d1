package io.renren.modules.enterprise.kq01.dao;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq01.dto.DevicePageDTO;
import io.renren.modules.enterprise.kq01.entity.DeviceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 考勤设备注册
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-11
 */
@Mapper
public interface DeviceDao extends BaseDao<DeviceEntity> {
    /**
     * 查询设备序列号
     * @param sn 设备序列号
     * @return int
     */
    int selectCountBySn(String sn);


    /**
     * 供应商添加/绑定设备列表页
     * @param params
     * @return
     */
    IPage<DevicePageDTO> pageList(@Param("page") IPage page, @Param("params") Map<String, Object> params) ;
}