package io.renren.modules.enterprise.cp01.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.utils.aliyun.CompanyDTO;
import io.renren.modules.enterprise.cp01.dto.Cp01DTO;
import io.renren.modules.enterprise.cp01.dto.RegisterDto;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;

import java.util.List;
import java.util.Map;

/**
 * 企业基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
public interface Cp01Service extends IService<Cp01Entity> {
    /**
     * 功能描述: <br> 根据社会统一信用代码查询企业基础信息 <br/>
     * 创建时间:  2020-06-29 14:16
     * @param corpCode 统一社会信用代码
     * @return  Cp01DTO
     * <AUTHOR>
     */
    Cp01DTO selectByCorpCode(String corpCode);

    /**
     * 列表分页查询
     * @param params params
     * @return PageData<Cp01DTO>
     */
    PageData<Cp01DTO> pageList(Map<String, Object> params);

    /**
     * 根据主键加载数据
     * @param id cp0101
     * @return Cp01DTO
     */
    Cp01DTO getInfo(Long id);

    /**
     * 保存信息
     * @param dto
     */
    void saveInfo(Cp01DTO dto);

    /**
     * 修改信息
     * @param dto
     */
    void updateInfo(Cp01DTO dto);

    /**
     * 企业注册
     * @param dto
     */
    Result register(RegisterDto dto);

    Result getCompanyInfo(String corpname);

    /**
     * 获取企业信息
     * @return
     */
    Result<List<CommonDto>> getCompanyList() ;
    /**
     * 获取已注册企业信息
     * @return
     */
    Result<List<CommonDto>> getRegisterCompanyList();

    /**
     * 输入框搜索建议数据
     * @return
     */
    Result<List<CommonDto>> querySearch();
}