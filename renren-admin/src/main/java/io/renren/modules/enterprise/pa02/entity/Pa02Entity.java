package io.renren.modules.enterprise.pa02.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
*<AUTHOR>
*@date 2021/10/9
*/


@Data
@EqualsAndHashCode
@TableName("B_PA02")
@KeySequence("SEQ_B_PA02")
public class Pa02Entity {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @TableId
    private Long pa0201;

    /**
     * 项目ID
     */
    private String pj0101;

    /**
     * 交易流水ID
     */
    private String detialid;
    /**
     * 账户账号
     */
    private String comaccount;


    /**
     * 账户户名
     */
    private String comaccountname;

    /**
     * 收支方式
     */
    private String accounttype;

    /**
     * 交易日期
     */
    private Date accountdate;

    /**
     * 金额
     */
    private String accountnum;

    /**
     * 币种(dic)
     */
    private String capitalcurrencytype;

    /**
     * 余额
     */
    private String blance;

    /**
     * 机构号
     */
    private String institutionnumber;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 对方账号
     */
    private String partaccount;

    /**
     * 对方户名
     */
    private String partname;

    /**
     * 摘要
     */
    private String description;

    /**
     * 发放对应工资日期
     */
    private Date issuedate;


}
