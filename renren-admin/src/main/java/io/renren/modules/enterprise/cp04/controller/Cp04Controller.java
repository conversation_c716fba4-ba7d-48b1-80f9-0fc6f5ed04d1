package io.renren.modules.enterprise.cp04.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.enterprise.cp04.dto.Cp04DTO;
import io.renren.modules.enterprise.cp04.dto.Cp04DetailDTO;
import io.renren.modules.enterprise.cp04.dto.Cp04PageDTO;
import io.renren.modules.enterprise.cp04.excel.Cp04Excel;
import io.renren.modules.enterprise.cp04.service.Cp04Service;
import io.renren.modules.enterprise.cp04.vo.Cp04SaveVO;
import io.renren.modules.enterprise.cp04.vo.Cp04UpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 分包信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-22
 */
@RestController
@RequestMapping("enterprise/cp04")
@Api(tags = "分包信息表")
public class Cp04Controller {
    @Autowired
    private Cp04Service cp04Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "cpname", value = "企业名称", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:cp04:page")
    public Result<PageData<Cp04PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Cp04PageDTO> page = cp04Service.getPageList(params);

        return new Result<PageData<Cp04PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:cp04:info")
    public Result<Cp04DetailDTO> get(@PathVariable("id") Long id) {

        return new Result<Cp04DetailDTO>().ok(cp04Service.getInfo(id));
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:cp04:save")
    public Result save(@RequestBody Cp04SaveVO cp04SaveVO) {
        //效验数据
        ValidatorUtils.validateEntity(cp04SaveVO);

        return cp04Service.save(cp04SaveVO);
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:cp04:update")
    public Result update(@RequestBody Cp04UpdateVO cp04UpdateVO) {
        //效验数据
        ValidatorUtils.validateEntity(cp04UpdateVO);

        return cp04Service.update(cp04UpdateVO);
    }

    @GetMapping("/getCompanyList")
    @ApiOperation("获取当前项目下企业")
    public Result<List<CommonDto>> getCompanyList() {

        return cp04Service.getCompanyList();
    }

}