package io.renren.modules.enterprise.cg07.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cg06.dto.TransferDTO;
import io.renren.modules.enterprise.cg07.entity.Cg07Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 区域档案上传配置
*
* @<NAME_EMAIL>
* @since 3.0 2023-11-06
*/
@Mapper
public interface Cg07Dao extends BaseDao<Cg07Entity> {

    List<TransferDTO> getTree(Long cg0401);

    boolean deleteByPj0101(Long pj0101);


}