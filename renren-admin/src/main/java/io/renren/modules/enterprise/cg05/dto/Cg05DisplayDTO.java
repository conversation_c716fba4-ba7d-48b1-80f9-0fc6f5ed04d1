package io.renren.modules.enterprise.cg05.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 档案展示实体
*
* @<NAME_EMAIL>
* @since 3.0  2025/05/22
*/
@Data
@ApiModel(value = "档案展示实体")
public class Cg05DisplayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cg0501;
    @ApiModelProperty(value = "配置阶段ID")
    private Long cg0401;
    @ApiModelProperty(value = "阶段附件配置ID")
    private Long cg0701;
    @ApiModelProperty(value = "档案代码")
    private String cg0502;
    @ApiModelProperty(value = "是否必填")
    private String cg0503;
    @ApiModelProperty(value = "档案说明")
    private String cg0504;
    @ApiModelProperty(value = "档案模板附件URL")
    private String cg0505;
    @ApiModelProperty(value = "是否有效")
    private String cg0506;
    @ApiModelProperty(value = "排序序号")
    private Long cg0507;
    @ApiModelProperty(value = "档案类型")
    private String cg0508;
    @ApiModelProperty(value = "所属年月")
    private String ssny;
    @ApiModelProperty(value = "上传表id")
    private Long cg0801;
    @ApiModelProperty(value = "模板附件Id")
    private Long ot0101;
    @ApiModelProperty(value = "是否上传(0，未上传  1，已上传)")
    private String cg0802;
    @ApiModelProperty(value = "创建日期")
    private Date createDate;
    @ApiModelProperty(value = "最后上传时间")
    private Date uploaddate;

}