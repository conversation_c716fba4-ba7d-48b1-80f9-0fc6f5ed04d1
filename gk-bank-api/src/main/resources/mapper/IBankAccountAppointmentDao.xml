<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.bank.business.dao.IBankAccountAppointmentDao">

    <resultMap type="com.xygk.bank.business.entity.IBankAccountAppointmentEntity" id="iBankAccountAppointmentMap">
        <result property="id" column="ID"/>
        <result property="taskId" column="TASK_ID"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="dotnumber" column="DOTNUMBER"/>
        <result property="reservationDate" column="RESERVATION_DATE"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="remarks" column="REMARKS"/>
    </resultMap>
    <select id="selectListByTaskId" resultType="com.xygk.bank.business.dto.IBankAccountAppointmentDTO"
            parameterType="java.lang.Long">
        select a.id,b.name project_name,b.linkman contact_person,b.linkphone contact_number,
               a.dotnumber,a.reservation_date,b.address project_address,d.corpcode,d.corpname corp_name
        from i_bank_account_appointment a,b_pj01 b,b_cp02 c,b_cp01 d
        where a.pj0101=b.pj0101 and b.pj0101 = c.pj0101 and c.cp0101 = d.cp0101 and c.corptype = 9
          and a.task_id=#{taskId}
    </select>


</mapper>