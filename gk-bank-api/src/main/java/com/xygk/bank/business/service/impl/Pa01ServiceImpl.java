package com.xygk.bank.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.bank.business.dao.Pa01Dao;
import com.xygk.bank.business.dto.Pa01DTO;
import com.xygk.bank.business.entity.Pa01Entity;
import com.xygk.bank.business.service.Pa01Service;
import com.xygk.bank.common.service.impl.CrudServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 工资专户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-14
 */
@Service
public class Pa01ServiceImpl extends CrudServiceImpl<Pa01Dao, Pa01Entity, Pa01DTO> implements Pa01Service {

    @Override
    public QueryWrapper<Pa01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pa01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}
