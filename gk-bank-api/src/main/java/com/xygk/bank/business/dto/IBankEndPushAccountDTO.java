package com.xygk.bank.business.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 停止推送专户
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-06
 */
@Data
@ApiModel(value = "停止推送专户")
public class IBankEndPushAccountDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(serialize = false)
    @ApiModelProperty(value = "ID")
    private Long id;
    @JSONField(serialize = false)
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "备注")
    private String mxdescription;
    @JSONField(serialize = false)
    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "专户账号")
    private String banknumber;
    @ApiModelProperty(value = "专户户名")
    private String banknumbername;

}