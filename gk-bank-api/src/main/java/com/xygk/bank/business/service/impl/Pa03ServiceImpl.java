package com.xygk.bank.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.bank.business.dao.Pa03Dao;
import com.xygk.bank.business.dto.Pa03DTO;
import com.xygk.bank.business.entity.Pa03Entity;
import com.xygk.bank.business.service.Pa03Service;
import com.xygk.bank.common.service.impl.CrudServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-18
 */
@Service
public class Pa03ServiceImpl extends CrudServiceImpl<Pa03Dao, Pa03Entity, Pa03DTO> implements Pa03Service {

    @Override
    public QueryWrapper<Pa03Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pa03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}
