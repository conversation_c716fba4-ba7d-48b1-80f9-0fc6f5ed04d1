package com.xygk.bank.business.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 银行接口服务
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("I_BANK_INTERFACE_SERVICE")
public class IBankInterfaceServiceEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     * 银行代码
     */
    private String bankcode;
    /**
     * 服务地址
     */
    private String url;
    /**
     * 类型
     */
    private String type;
    /**
     * 序列
     */
    private Short orders;
    /**
     * 备注
     */
    private String note;
}
