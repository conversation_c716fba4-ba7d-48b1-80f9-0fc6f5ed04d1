package com.xygk.bank.business.service;

import com.xygk.bank.business.dto.IBankFileNoticeLogDTO;
import com.xygk.bank.business.entity.IBankFileNoticeLogEntity;
import com.xygk.bank.common.service.CrudService;

/**
 * 文件通知日志接口
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/3/14 9:52
 **/
public interface IBankFileNoticeLogService extends CrudService<IBankFileNoticeLogEntity, IBankFileNoticeLogDTO> {

    /**
     * FileNotice日志保存
     *
     * @param bankcode
     * @param filename
     * @param filenoticecontent
     */
    void saveFileNoticeLog(String bankcode, String filename, String filenoticecontent);
}
