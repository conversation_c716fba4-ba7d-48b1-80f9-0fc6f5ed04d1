package com.xygk.bank.business.dao;

import com.xygk.bank.business.entity.IBankTaskEntity;
import com.xygk.bank.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 银行任务表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-14
 */
@Mapper
public interface IBankTaskDao extends BaseDao<IBankTaskEntity> {
    /**
     *
     * @description 查询需要写文件的任务
     * <AUTHOR>
     * @date 2022年11月14日 14:20
     */
    IBankTaskEntity selectNeedWriteToFile(@Param("taskStatus") String taskStatus);

    /**
     * @description: 生成核验任务数据
     * @author: CJF
     * @date: 2023/2/7 15:00
     * @param:
     * @return:
     **/
    String generateData(Map<String,String> map);
}
