package com.xygk.bank.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 银行接口日志
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-02
 */
@Data
@ApiModel(value = "银行接口日志")
public class IBankLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "请求URI")
    private String requestUri;
    @ApiModelProperty(value = "请求方式")
    private String requestMethod;
    @ApiModelProperty(value = "请求参数")
    private String requestParams;
    @ApiModelProperty(value = "通向（1：进，2：出）")
    private String leadsTo;
    @ApiModelProperty(value = "用户名")
    private String creatorName;
    @ApiModelProperty(value = "响应参数")
    private String responseParams;
    @ApiModelProperty(value = "异常信息")
    private String exmsg;

}
