package com.xygk.bank.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;

/**
 * <AUTHOR>
 * @title SM4Util
 * @Description SM4工具类
 * @Date 2022/11/7 14:58
 * @Copyright 2019-2025
 */
public class SM4Util {
    public static Mode mode = Mode.CBC;
    public static Padding padding = Padding.PKCS5Padding;
    /**
     *
     * @description 解密
     * <AUTHOR>
     * @date 2022年11月07日 16:06
     */
    public static byte[] decrypt(String data, String secretkey, String iv) {
        SM4 sm4 = new SM4(mode, padding, secretkey.getBytes(), iv.getBytes());
        return sm4.decrypt(Base64.decode(data));
    }
    /**
     *
     * @description 加密
     * <AUTHOR>
     * @date 2022年11月07日 16:06
     */
    public static String encrypt(byte[] data, String secretkey, String iv) {
        SM4 sm4 = new SM4(mode, padding, secretkey.getBytes(), iv.getBytes());
        return Base64.encode(sm4.encrypt(data));
    }
}
