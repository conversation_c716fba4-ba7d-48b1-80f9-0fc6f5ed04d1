package com.xygk.bank.common.exception;
/**
 *
 * @description 系统全局异常
 * <AUTHOR>
 * @date 2020年02月11日 10:27
 */
public class SystemException extends RuntimeException{
    private int code;
    private String msg;

    public SystemException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public SystemException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public SystemException() {
        super();
    }
}
