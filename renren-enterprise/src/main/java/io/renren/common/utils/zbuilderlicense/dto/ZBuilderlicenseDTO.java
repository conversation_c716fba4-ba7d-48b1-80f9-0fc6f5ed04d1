package io.renren.common.utils.zbuilderlicense.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 迁移施工许可证信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-23
 */
@Data
@ApiModel(value = "迁移施工许可证信息表")
public class ZBuilderlicenseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "工程名称")
	private String prjname;
		@ApiModelProperty(value = "安监备案号")
	private String safetyno;
		@ApiModelProperty(value = "施工许可证号")
	private String builderlicensenum;
		@ApiModelProperty(value = "发证机关")
	private String organname;
		@ApiModelProperty(value = "发证日期")
	private String organdate;
		@ApiModelProperty(value = "项目id")
	private String projectid;

}