package io.renren.common.utils.zproject.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 迁移项目信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("Z_PROJECT")
public class ZProjectEntity  {

    /**
     * 项目名称
     */
	private String name;
    /**
     * 项目状态
     */
	@TableField("prjstatus")
	private String prjStatus;
    /**
     * 项目简介
     */
	private String description;
    /**
     * 项目类别
     */
	private String category;
    /**
     * 建设性质
     */
	@TableField("constructtype")
	private String constructType;
    /**
     * 投资类型
     */
	@TableField("investtype")
	private String investType;
    /**
     * 项目所在地
     */
	@TableField("areacode")
	private String areaCode;
    /**
     * 建设地址
     */
	private String address;
    /**
     * 总面积
     */
	@TableField("buildingarea")
	private String buildingArea;
	/**
	 * 总长度
	 */
	@TableField("buildinglength")
	private String buildingLength;
    /**
     * 总投资
     */
	private String invest;
    /**
     * 项目规模
     */
	private String scale;
    /**
     * 开工日期
     */
	@TableField("startdate")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date startDate;
    /**
     * 经度
     */
	private String lng;
    /**
     * 纬度
     */
	private String lat;
	/**
	 * 纬度
	 */
	@TableField("provincialCode")
	private String provincialcode;
    /**
     * 原项目id
     */
	private String projectid;
	/**
	 * 项目id
	 */
	private String pj0101;
	/**
	 * 是否迁移
	 */
	private String isconvert;
}