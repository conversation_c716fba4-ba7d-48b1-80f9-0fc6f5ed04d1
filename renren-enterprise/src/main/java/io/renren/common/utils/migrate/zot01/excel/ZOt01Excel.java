package io.renren.common.utils.migrate.zot01.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁移附件数据表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-04-04
 */
@Data
public class ZOt01Excel {
    @Excel(name = "业务类型(dic)")
    private String busitype;
    @Excel(name = "业务编号1")
    private BigDecimal busisysno1;
    @Excel(name = "业务编号2")
    private BigDecimal busisysno2;
    @Excel(name = "附件路径")
    private String url;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;

}