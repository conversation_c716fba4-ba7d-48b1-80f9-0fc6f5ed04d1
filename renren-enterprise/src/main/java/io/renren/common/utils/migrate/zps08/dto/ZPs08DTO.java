package io.renren.common.utils.migrate.zps08.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁移工人合同数据表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-04-04
 */
@Data
@ApiModel(value = "迁移工人合同数据表")
public class ZPs08DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "自编项目编码")
	private String zcode;
		@ApiModelProperty(value = "合同编号")
	private String contractno;
		@ApiModelProperty(value = "甲方（用工企业）")
	private String corpname;
		@ApiModelProperty(value = "法定代表人或委托代理人")
	private String legalman;
		@ApiModelProperty(value = "统一社会信用代码")
	private String corpcode;
		@ApiModelProperty(value = "单位地址")
	private String corpaddress;
		@ApiModelProperty(value = "联系电话")
	private String linkcellphone;
		@ApiModelProperty(value = "乙方姓名")
	private String name;
		@ApiModelProperty(value = "性别")
	private String gender;
		@ApiModelProperty(value = "出生年月")
	private Date birthday;
		@ApiModelProperty(value = "身份证号码")
	private String idcardnumber;
		@ApiModelProperty(value = "居住地址")
	private String address;
		@ApiModelProperty(value = "联系电话")
	private String cellphone;
		@ApiModelProperty(value = "合同开始时间")
	private String contractbegin;
		@ApiModelProperty(value = "工程名称")
	private String prjname;
		@ApiModelProperty(value = "项目名称")
	private String projectname;
		@ApiModelProperty(value = "工种")
	private String worktypecode;
		@ApiModelProperty(value = "职责")
	private String duty;
		@ApiModelProperty(value = "工资支付方式")
	private String salarypaymentmethod;
		@ApiModelProperty(value = "每月计时工资")
	private String timewage;
		@ApiModelProperty(value = "工作量")
	private String workload;
		@ApiModelProperty(value = "工作量工资")
	private String workloadwage;
		@ApiModelProperty(value = "其他支付形式")
	private String otherwage;
		@ApiModelProperty(value = "工资发放日")
	private String payday;
		@ApiModelProperty(value = "工时制度")
	private String workhoursystem;
		@ApiModelProperty(value = "每天工作小时")
	private String workhour;
		@ApiModelProperty(value = "每周工作天数")
	private String workday;
		@ApiModelProperty(value = "不定时工时制")
	private String workirregular;
		@ApiModelProperty(value = "福利")
	private String welfare;
		@ApiModelProperty(value = "委托代理人")
	private String consignor;
		@ApiModelProperty(value = "工作地点")
	private String workeraddress;
		@ApiModelProperty(value = "其他补充事项")
	private String otherthings;
		@ApiModelProperty(value = "合同签订时间")
	private String signdate;
				@ApiModelProperty(value = "省厅上报状态（dic）")
	private String stupstatus;

}