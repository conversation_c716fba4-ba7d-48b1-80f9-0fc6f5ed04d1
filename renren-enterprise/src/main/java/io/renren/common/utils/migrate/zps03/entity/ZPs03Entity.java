package io.renren.common.utils.migrate.zps03.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 迁移管理人员数据表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("Z_PS03")
public class ZPs03Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 证件号码
     */
	private String idcardnumber;
    /**
     * 自编项目编码
     */
	private String zcode;
    /**
     * 社会统一信用代码
     */
	private String corpcode;
    /**
     * 岗位类型（dic）
     */
	private String jobtype;
    /**
     * 项目采集照片
     */
	private String photo;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态（dic）
     */
	private String inOrOut;
    /**
     * 省厅上报状态（dic）
     */
	private String stupstatus;
}