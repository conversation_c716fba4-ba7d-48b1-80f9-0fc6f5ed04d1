package io.renren.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.renren.common.common.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Slf4j
public class HttpUtil {

    private static final String KEY_ID = "84983712-7cda-11e7-857d-00163e32d704";
    private static final String signature_format = "%s_%s_%s";




    /**
     * 发送请求 FormData
     */
    public static <T> List<T> doPostUrl(String url, MultiValueMap<String, Object> map, Class<T> clazz) {
        String rcode = RandomUtil.randomString(10);

        String ts = Long.toString(System.currentTimeMillis() / 1000L);
        String signature = null;
        try {
            signature = Sha1Util.shaEncode(String.format(signature_format, rcode, ts, KEY_ID));
        } catch (Exception e) {
            log.error("密钥加密失败");
        }

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("keyId", KEY_ID);
        headers.set("ts", ts);
        headers.set("rcode", rcode);
        headers.set("signature", signature);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(map, headers);
        //发起请求,服务地址，请求参数，返回消息体的数据类型
        ResponseEntity<String> response = restTemplate.postForEntity(url, param, String.class);

        String body = response.getBody();

        Response res = JSONObject.parseObject(body, Response.class);

        return JSONObject.parseArray(res.getData(), clazz);
    }

    /**
     * 发送请求 FormData
     */
    public static <T> List<T> doGetUrl(String url, Map<String, Object> map, Class<T> clazz) {
        String rcode = RandomUtil.randomString(10);

        String ts = Long.toString(System.currentTimeMillis() / 1000L);
        String signature = null;
        try {
            signature = Sha1Util.shaEncode(String.format(signature_format, rcode, ts, KEY_ID));
        } catch (Exception e) {
            log.error("密钥加密失败");
        }

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("keyId", KEY_ID);
        headers.set("ts", ts);
        headers.set("rcode", rcode);
        headers.set("signature", signature);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(null, headers);


        //发起请求,服务地址，请求参数，返回消息体的数据类型
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, param, String.class, map);

        String body = response.getBody();

        Response res = JSONObject.parseObject(body, Response.class);

        return JSONObject.parseArray(res.getData(), clazz);
    }

    /**
     * 发送请求 FormData
     */
    public static <T> List<T> doPostUrlV2(String url, MultiValueMap<String, Object> map, Class<T> clazz) {
        String rcode = RandomUtil.randomString(10);

        String ts = Long.toString(System.currentTimeMillis() / 1000L);
        String signature = null;
        try {
            signature = Sha1Util.shaEncode(String.format(signature_format, rcode, ts, KEY_ID));
        } catch (Exception e) {
            log.error("密钥加密失败");
        }

//        RestTemplate restTemplate = new RestTemplate();
        RestTemplate restTemplate = SingletonRestTemplate.getInstance();
        HttpHeaders headers = new HttpHeaders();
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("keyId", KEY_ID);
        headers.set("ts", ts);
        headers.set("rcode", rcode);
        headers.set("signature", signature);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(map, headers);
        //发起请求,服务地址，请求参数，返回消息体的数据类型
        ResponseEntity<String> response = restTemplate.postForEntity(url, param, String.class);

        String body = response.getBody();

        Response res = JSONObject.parseObject(body, Response.class);

        return JSONObject.parseArray(res.getData(), clazz);
    }

    /**
     * 发送请求 FormData
     */
    public static <T> List<T> doPosPagetUrl(String url, MultiValueMap<String, Object> map, Class<T> clazz) {
        String rcode = RandomUtil.randomString(10);

        String ts = Long.toString(System.currentTimeMillis() / 1000L);
        String signature = null;
        try {
            signature = Sha1Util.shaEncode(String.format(signature_format, rcode, ts, KEY_ID));
        } catch (Exception e) {
            log.error("密钥加密失败");
        }

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("keyId", KEY_ID);
        headers.set("ts", ts);
        headers.set("rcode", rcode);
        headers.set("signature", signature);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(map, headers);
        //发起请求,服务地址，请求参数，返回消息体的数据类型
        ResponseEntity<String> response = restTemplate.postForEntity(url, param, String.class);

        String body = response.getBody();

        Response res = JSONObject.parseObject(body, Response.class);

        return JSONObject.parseArray(res.getData(), clazz);
    }

    /**
     * 获取数据
     * @param url
     * @param projectId
     * @param pageIndex
     * @param size
     * @return
     */
    public static <T> List<T> getData(String url, String projectId, Integer pageIndex, Integer size, Class clazz) {

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>() ;
        params.add("page", pageIndex) ;
        params.add("size", size) ;
        params.add("projectId", projectId) ;
        Response response = doPostSourceUrl(url, params);
        JSONObject responseData = doCovertResponse(response);
        String data = responseData.getString("records");
        // 转换
        return JSON.parseArray(data, clazz);
    }

    /**
     * 获取分页条目数
     * @return
     */
    public static Integer getPageTotal(String url, String projectId, Integer size, Class clazz) {

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>() ;
        params.add("page", 1) ;
        params.add("size", size) ;
        params.add("projectId", projectId) ;
        Response response = doPostSourceUrl(url, params);
        JSONObject responseData = doCovertResponse(response);
        if (ObjectUtil.isEmpty(responseData)) {
            return 0;
        }
        // 转换
        String total = responseData.getString("pages");
        return StringUtils.isBlank(total)?0:Integer.valueOf(total);
    }

    /**
     * 对返回数据做整理
     * @return
     */
    public static JSONObject doCovertResponse(Response response) {

        String data = response.getData();
        return JSONObject.parseObject(data, JSONObject.class) ;
    }

    /**
     * 发送请求 FormData
     */
    public static Response doPostSourceUrl(String url, MultiValueMap<String, Object> map) {

        String rcode = RandomUtil.randomString(10);

        String ts = Long.toString(System.currentTimeMillis() / 1000L);
        String signature = null;
        try {
            signature = Sha1Util.shaEncode(String.format(signature_format, rcode, ts, KEY_ID));
        } catch (Exception e) {
            log.error("Error signing signature", e.getMessage());
        }

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("keyId", KEY_ID);
        headers.set("ts", ts);
        headers.set("rcode", rcode);
        headers.set("signature", signature);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(map, headers);
        //发起请求,服务地址，请求参数，返回消息体的数据类型
        ResponseEntity<String> response = restTemplate.postForEntity(url, param, String.class);

        String body = response.getBody();

        return JSONObject.parseObject(body, Response.class);
    }
}
