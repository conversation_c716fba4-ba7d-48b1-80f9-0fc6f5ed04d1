package io.renren.common.utils.zteam.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 迁移项目班组信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("Z_TEAM")
public class ZTeamEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 项目Id
     */
	private String projectid;
    /**
     * 班组编号
     */
	@TableField("teamSysNo")
	private String teamsysno;
    /**
     * 班组所在企业统一社会信用代码
     */
	@TableField("corpcode")
	private String corpCode;
    /**
     * 班组所在企业名称
     */
	@TableField("corpname")
	private String corpName;
    /**
     * 班组名称
     */
	@TableField("teamname")
	private String teamName;
    /**
     * 班组长姓名
     */
	@TableField("teamLeaderName")
	private String teamleadername;
    /**
     * 班组长联系电话
     */
	@TableField("teamLeaderPhone")
	private String teamleaderphone;
}