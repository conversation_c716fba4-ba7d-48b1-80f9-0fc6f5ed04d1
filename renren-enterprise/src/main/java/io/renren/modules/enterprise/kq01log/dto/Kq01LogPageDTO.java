package io.renren.modules.enterprise.kq01log.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-04-28 13:27
 */
@Data
@ApiModel(value = "考勤设备分页信息")
public class Kq01LogPageDTO implements Serializable {
    private static final long serialVersionUID = -2202853159762564871L;

    @ApiModelProperty(value = "主键ID")
    private Long kq0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "设备类型")
    private String terminaltype;

    @ApiModelProperty(value = "设备序列号")
    private String deviceserialno;

    @ApiModelProperty(value = "设备注册码")
    private String devicekey;

    @ApiModelProperty(value = "进出状态")
    private String note;

    @ApiModelProperty(value = "网络状态")
    private String networkStatus;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
