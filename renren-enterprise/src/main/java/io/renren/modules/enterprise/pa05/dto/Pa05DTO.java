package io.renren.modules.enterprise.pa05.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人工资单
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "工人工资单")
public class Pa05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pa0501;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @ApiModelProperty(value = "工资发放月份")
    private Date issuedate;
    @ApiModelProperty(value = "工人数量")
    private Integer workercount;
    @ApiModelProperty(value = "工资发放总金额")
    private BigDecimal accountnum;
    @ApiModelProperty(value = "工资单状态")
    private String salarystatus;

}