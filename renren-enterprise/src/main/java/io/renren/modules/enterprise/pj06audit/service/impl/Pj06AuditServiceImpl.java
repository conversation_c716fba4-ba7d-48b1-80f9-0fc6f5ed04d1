package io.renren.modules.enterprise.pj06audit.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.common.service.CommonService;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.enterprise.pj06.dao.Pj06Dao;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.service.Pj06Service;
import io.renren.modules.enterprise.pj06audit.dao.Pj06AuditDao;
import io.renren.modules.enterprise.pj06audit.dto.AuditDTO;
import io.renren.modules.enterprise.pj06audit.dto.Pj06AuditDTO;
import io.renren.modules.enterprise.pj06audit.entity.Pj06AuditEntity;
import io.renren.modules.enterprise.pj06audit.service.Pj06AuditService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-27
 */
@Service
public class Pj06AuditServiceImpl extends CrudServiceImpl<Pj06AuditDao, Pj06AuditEntity, Pj06AuditDTO> implements Pj06AuditService {
    @Autowired
    private Pj06AuditDao pj06AuditDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysSmsService sysSmsService;
    @Autowired
    private Pj06Dao pj06Dao;
    @Autowired
    private Pj06Service pj06Service;

    @Override
    public QueryWrapper<Pj06AuditEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj06AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<Pj06AuditDTO> pageList(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        params.put("cp0101", commonService.getUserCp0101());
        Page<Pj06AuditDTO> page = new Page<>(curPage, limit);
        List<Pj06AuditDTO> list = pj06AuditDao.getList(page, params);
        return getPageData(list, page.getTotal(), Pj06AuditDTO.class);
    }

    @Override
    public void audit(AuditDTO dto) {
        Pj06AuditEntity pj06AuditEntity = baseDao.selectOne(new QueryWrapper<Pj06AuditEntity>()
                .eq("id", dto.getId()));
        pj06AuditEntity.setAuditstatus(dto.getAuditstatus());
        pj06AuditEntity.setAuditresult(dto.getAuditresult());
        pj06AuditEntity.setAuditdate(new Date());

        baseDao.updateById(pj06AuditEntity);
        Pj06DTO pj06DTO = dto.getPj06DTO();
        // 审核不通过
        if ("2".equals(dto.getAuditstatus())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("notPassMsg", dto.getAuditresult());
            sysSmsService.send("1002", pj06DTO.getLinkphone(), jsonObject.toJSONString());
        }
        // 如果 3家企业都审核通过，则生成账号
        Integer count = baseDao.selectCount(new QueryWrapper<Pj06AuditEntity>()
                .eq("pj0601", pj06DTO.getPj0601())
                .eq("auditstatus", "1"));
        if (count == 3) {
            pj06Service.saveCheckProjectInfo(pj06DTO);
        }
    }
}