package io.renren.modules.enterprise.ps04.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Data
public class Ps04Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps0401;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "参建单位ID")
    private BigDecimal cp0201;
    @Excel(name = "人员ID")
    private BigDecimal ps0301;
    @Excel(name = "岗位类型")
    private String jobtype;
    @Excel(name = "项目采集照片")
    private String photo;
    @Excel(name = "进场时间")
    private Date entrytime;
    @Excel(name = "退场时间")
    private Date exittime;
    @Excel(name = "进退场状态")
    private String inOrOut;
    @Excel(name = "审核状态")
    private String auditstatus;
    @Excel(name = "审核结果")
    private String auditresult;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}