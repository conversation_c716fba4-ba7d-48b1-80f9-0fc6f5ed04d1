package io.renren.modules.enterprise.pj06.service.impl;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.redis.RedisKeys;
import io.renren.common.redis.RedisUtils;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.admin.sys.dao.SysDeptDao;
import io.renren.modules.admin.sys.dao.SysRoleDao;
import io.renren.modules.admin.sys.dao.SysUserDao;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.admin.sys.service.SysUserService;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.dto.Cp01DTO;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.cp02audit.dao.Cp02AuditDao;
import io.renren.modules.enterprise.cp03.dto.BCp03DTO;
import io.renren.modules.enterprise.cp03.service.BCp03Service;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.pj01report.dao.Pj01ReportDao;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;
import io.renren.modules.enterprise.pj06.dao.Pj06Dao;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;
import io.renren.modules.enterprise.pj06.service.Pj06AsyncService;
import io.renren.modules.enterprise.pj06.service.Pj06Service;
import io.renren.modules.enterprise.pj06audit.dao.Pj06AuditDao;
import io.renren.modules.enterprise.pj06audit.entity.Pj06AuditEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj06ServiceImpl extends CrudServiceImpl<Pj06Dao, Pj06Entity, Pj06DTO> implements Pj06Service {
    @Autowired
    private Pj06Service pj06Service;
    @Autowired
    private Pj06Dao pj06Dao;
    @Autowired
    private Pj06AuditDao pj06AuditDao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private SysDeptDao sysDeptDao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Cp02AuditDao cp02AuditDao;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysSmsService sysSmsService;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private BCp03Service cp03Service;
    @Autowired
    private Pj01ReportDao pj01ReportDao;

    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private Pj06AsyncService pj06AsyncService;
    @Autowired
    private RedisUtils redisUtils;
    private static final String REVIEW_SUCCESS = "1";
    private static final String REVIEW_ERROR = "2";

    @Override
    public QueryWrapper<Pj06Entity> getWrapper(Map<String, Object> params) {
        String name = (String) params.get("name");
        QueryWrapper<Pj06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(name), "name", name);
        wrapper.orderByDesc("CREATE_DATE");
        return wrapper;
    }

    @Override
    public void saveCheckProjectInfo(Pj06DTO dto) {
        //数据写入到SYS_DEPT,创建机构
        Long deptId = pj06Service.saveDeptInfo(dto);
        //数据写入到PJ01,新增项目基础信息
        Long pj0101 = pj06Service.savePj01Info(dto, deptId);
        // 保存参建信息
        saveCp02(dto, pj0101);
        //数据写入到SYS_USER,创建用户
        SysUserDTO sysUserDTO = pj06Service.saveUserInfo(dto, deptId);
        //调用生成关系表过程
        sysUserDao.callRPj01Dept(pj0101);
        //发送用户名和密码到注册项目的手机号码
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("username", sysUserDTO.getUsername());
        jsonObject.put("password", sysUserDTO.getPassword());
        sysSmsService.send("1001", dto.getLinkphone(), jsonObject.toJSONString());
        //更新项目审核数据表PJ06
        dto.setStatus("1");
        pj06Service.update(dto);
    }

    private void saveCp02(Pj06DTO dto, Long pj0101) {
        List<Pj06AuditEntity> pj0601s = pj06AuditDao.selectList(new QueryWrapper<Pj06AuditEntity>()
                .eq("pj0601", dto.getPj0601()));

        for (Pj06AuditEntity pj0601 : pj0601s) {
            Cp02Entity cp02 = new Cp02Entity();
            cp02.setPj0101(pj0101);
            cp02.setCp0101(pj0601.getCp0101());
            cp02.setCorptype(pj0601.getCorptype());
            cp02.setInOrOut("1");
            cp02.setEntrytime(new Date());
            cp02Dao.insert(cp02);
        }
    }

    @Override
    public void saveProjectInfo(Pj06DTO dto) {
        String registerKey = RedisKeys.getProjectRegisterKey(dto.getLinkphone());
        // 校验验证码是否正确
        if (!StrUtil.equals(dto.getRegisterCode(), redisUtils.getString(registerKey))) {
            throw new RenException("输入验证码错误");
        }
        //先判断PJ06是否存在项目
        Long projectNumber = pj06Dao.getProjectNumber(dto.getName(), dto.getAreacode());
        if (projectNumber > 0) {
            throw new RenException("项目已注册,请您耐心等待审核！");
        }
        //在判断PJ01是否存在项目
        Long pj01Number = pj06Dao.getPj01Number(dto.getName(), dto.getAreacode());
        if (pj01Number > 0) {
            throw new RenException("项目已存在,请勿重复注册！");
        }
        //设置默认审核状态
        // 需求设置项目不审核
        dto.setStatus("1");
        pj06Service.save(dto);

        saveCheckProjectInfo(dto);
        // 操作成功之后 删除验证码
        redisUtils.delete(registerKey);
    }

    //数据写入到SYS_DEPT,创建机构
    @Override
    public Long saveDeptInfo(Pj06DTO dto) {
        SysDeptEntity sysDeptEntity = new SysDeptEntity();
        // 主管部门areacode
        sysDeptEntity.setAreacode(dto.getVirareacode());
        sysDeptEntity.setPid(92733L);
        sysDeptEntity.setName(dto.getName());
        sysDeptDao.insert(sysDeptEntity);
        return sysDeptEntity.getId();
    }

    @Override
    public Long savePj01Info(Pj06DTO dto, Long deptId) {
        Pj01Entity pj01Entity = new Pj01Entity();
        pj01Entity.setName(dto.getName());
        pj01Entity.setAreacode(dto.getAreacode());
        pj01Entity.setLinkman(dto.getLinkman());
        pj01Entity.setLinkphone(dto.getLinkphone());
        pj01Entity.setDeptId(deptId);
        //新注册的项目默认设置为在建项目
        pj01Entity.setPrjstatus("3");
        pj01Dao.insert(pj01Entity);
        return pj01Entity.getPj0101();
    }

    @Override
    public void saveConstruction(Pj06DTO dto, Long pj0101) {
        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getConstructionnumber());
        Cp02Entity cp02Entity = new Cp02Entity();
        //如果参建不存在就新增信息到CP01
        if (ObjectUtil.isNull(cp01DTO)) {
            Cp01Entity construction = new Cp01Entity();
            construction.setCorpname(dto.getConstructionname());
            construction.setCorpcode(dto.getConstructionnumber());
            cp01Dao.insert(construction);
            cp02Entity.setCp0101(construction.getCp0101());
            //获取企业工商信息
            pj06AsyncService.saveEnterpriseBusinessInfo(construction.getCp0101(), dto.getConstructionnumber());
        } else {
            cp02Entity.setCp0101(cp01DTO.getCp0101());
        }
        cp02Entity.setCorptype("8");
        cp02Entity.setPj0101(pj0101);
        cp02Entity.setEntrytime(new Date());
        cp02Dao.insert(cp02Entity);
        //保存进退场记录表
        BCp03DTO cp03DTO = new BCp03DTO();
        cp03DTO.setCp0201(cp02Entity.getCp0201());
        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
        cp03DTO.setInOrOut("1");
        cp03Service.save(cp03DTO);
    }

    @Override
    public void saveContractUnit(Pj06DTO dto, Long pj0101) {
        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getContractnumber());
        Cp02Entity cp02Entity = new Cp02Entity();
        if (ObjectUtil.isNull(cp01DTO)) {
            Cp01Entity cp01Entity = new Cp01Entity();
            cp01Entity.setCorpname(dto.getContractname());
            cp01Entity.setCorpcode(dto.getContractnumber());
            cp01Dao.insert(cp01Entity);
            cp02Entity.setCp0101(cp01Entity.getCp0101());
            //获取企业工商信息
            pj06AsyncService.saveEnterpriseBusinessInfo(cp01Entity.getCp0101(), dto.getContractnumber());
        } else {
            cp02Entity.setCp0101(cp01DTO.getCp0101());
        }
        cp02Entity.setCorptype("9");
        cp02Entity.setPj0101(pj0101);
        cp02Entity.setEntrytime(new Date());
        cp02Dao.insert(cp02Entity);
        //保存进退场记录表
        BCp03DTO cp03DTO = new BCp03DTO();
        cp03DTO.setCp0201(cp02Entity.getCp0201());
        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
        cp03DTO.setInOrOut("1");
        cp03Service.save(cp03DTO);
    }

    @Override
    public SysUserDTO saveUserInfo(Pj06DTO dto, Long deptId) {
        SysUserDTO sysUserDTO = new SysUserDTO();
        sysUserDTO.setPassword(sysParamsService.getValue(Constant.PASSWORD));
        sysUserDTO.setDeptId(deptId);
        sysUserDTO.setRealName(dto.getLinkman());
        sysUserDTO.setMobile(dto.getLinkphone());
        sysUserDTO.setSuperAdmin(0);
        sysUserDTO.setStatus(1);
        sysUserDTO.setUserType("1");
        //生成用户名
        sysUserDTO.setUsername(pj06Service.generateUserName());
        // 用户角色
        List collect = new ArrayList();
        /*String[] split = dto.getRoleIdList().split(",");
        for (String s : split) {
            collect.add(Long.valueOf(s));
        }*/
        collect.add(1254300243697975297L);
        sysUserDTO.setRoleIdList(collect);
        sysUserService.save(sysUserDTO);
        return sysUserDTO;
    }

    @Override
    public String generateUserName() {
        String userName;
        //先查询当前行政区划的账号使用到哪个数字
        Long lastname = pj06Dao.selectUserName();
        userName = "YB" + lastname;
        return userName;
    }

    @Override
    public void savePj01Report(Long pj0101, String report, String payBankCode) {
        Pj01ReportEntity pj01ReportEntity = new Pj01ReportEntity();
        pj01ReportEntity.setPj0101(pj0101);
        pj01ReportEntity.setReportType(report);
        pj01ReportEntity.setPayBankCode(payBankCode);
        pj01ReportDao.insert(pj01ReportEntity);
    }

    @Override
    public List<CommonDto> getRoleList() {

        List<CommonDto> roleList = sysRoleDao.getRoleList();

        return roleList;
    }

    @Override
    public Result sendCode(String phone) {
        if (!Validator.isMatchRegex(PatternPool.MOBILE, phone)) {
            return new Result().error("请输入正确的手机号码");
        }

        String code = RandomUtil.randomNumbers(4);
        log.info(String.format("项目注册：手机号码[%s],验证码为[%s]", phone, code));
        redisUtils.set(RedisKeys.getProjectRegisterKey(phone), code);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("code", RedisKeys.getProjectRegisterKey(code));
//        sysSmsService.send("1003", phone, jsonObject.toJSONString());

        return new Result().ok("发送成功");
    }

    @Override
    public Pj06DTO getInfo(Long pj0601) {
        return baseDao.getInfo(pj0601);
    }
}