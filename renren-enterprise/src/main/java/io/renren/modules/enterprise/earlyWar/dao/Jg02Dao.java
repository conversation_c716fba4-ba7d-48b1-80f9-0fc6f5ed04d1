package io.renren.modules.enterprise.earlyWar.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.earlyWar.dto.Jg02DTO;
import io.renren.modules.enterprise.earlyWar.entity.Jg02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Mapper
public interface Jg02Dao extends BaseDao<Jg02Entity> {

    List<Jg02DTO> getList(Map<String, Object> params);

    Jg02DTO selectInfoById(Long jg0201);
}