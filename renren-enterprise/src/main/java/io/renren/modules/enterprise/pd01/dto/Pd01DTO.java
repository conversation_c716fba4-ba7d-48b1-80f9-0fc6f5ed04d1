package io.renren.modules.enterprise.pd01.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.renren.common.validator.group.DefaultGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 施工现场机械基本信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "施工现场机械基本信息")
public class Pd01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pd0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "环保信息标签编号")
    @NotBlank(message = "环保信息标签编号不能为空", groups = DefaultGroup.class)
    @Length(max = 20, message = "环保信息标签编号过长，不能超过20位", groups = DefaultGroup.class)
    private String tagno;

    @ApiModelProperty(value = "机械型号", required = true)
    @NotBlank(message = "机械型号不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "机型型号长度不能超过50位", groups = DefaultGroup.class)
    private String model;

    @ApiModelProperty(value = "机械名称", required = true)
    @NotBlank(message = "机械名称不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "机型名称长度不能超过50位", groups = DefaultGroup.class)
    private String devicename;

    @ApiModelProperty(value = "商标", required = true)
    @NotBlank(message = "商标不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "商标长度不能超过50位", groups = DefaultGroup.class)
    private String trademark;

    @ApiModelProperty(value = "机械类型", required = true)
    @NotBlank(message = "机械名称不能为空", groups = DefaultGroup.class)
    private String devicetype;

    @ApiModelProperty(value = "排放阶段", required = true)
    @NotBlank(message = "排放阶段不能为空", groups = DefaultGroup.class)
    private String dischargestage;

    @ApiModelProperty(value = "制造厂商", required = true)
    @NotBlank(message = "制造厂商不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "制造厂商长度不能超过50位", groups = DefaultGroup.class)
    private String manufacturer;

    @ApiModelProperty(value = "发动机型号", required = true)
    @NotBlank(message = "发动机型号不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "发动机型号长度不能超过50位", groups = DefaultGroup.class)
    private String enginetype;
    
    @ApiModelProperty(value = "出厂年月", required = true)
    @NotNull(message = "出厂年月不能为空", groups = DefaultGroup.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date factorymonthly;

    @ApiModelProperty(value = "产权单位", required = true)
    @NotBlank(message = "产权单位不能为空", groups = DefaultGroup.class)
    @Length(max = 50, message = "产权单位长度不能超过50位", groups = DefaultGroup.class)
    private String propertyunit;

    @ApiModelProperty(value = "建设单位统一社会信用代码", required = true)
    @NotBlank(message = "建设单位统一社会信用代码不能为空", groups = DefaultGroup.class)
    @IsCreditCodeValidator(message = "建设单位统一社会信用代码格式不正确", groups = DefaultGroup.class)
    private String creditcode;

    @ApiModelProperty(value = "备注")
    private String memo;


}