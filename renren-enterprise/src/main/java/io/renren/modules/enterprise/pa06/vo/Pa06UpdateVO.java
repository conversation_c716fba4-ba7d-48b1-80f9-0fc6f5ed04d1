package io.renren.modules.enterprise.pa06.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工资明细单
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "工资明细单")
public class Pa06UpdateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pa0601;
    @ApiModelProperty(value = "应发工资")
    private BigDecimal shouldsalary;
    @ApiModelProperty(value = "实发工资")
    private BigDecimal actualsalary;

}