package io.renren.modules.enterprise.kq03.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.exception.RenException;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.kq03.dao.Kq03Dao;
import io.renren.modules.enterprise.kq03.dto.Kq03DTO;
import io.renren.modules.enterprise.kq03.entity.Kq03Entity;
import io.renren.modules.enterprise.kq03.service.Kq03Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 设备注册码
 *
 * <AUTHOR>
 * @since 1.0.0 2020-05-25
 */
@Service
public class Kq03ServiceImpl extends CrudServiceImpl<Kq03Dao, Kq03Entity, Kq03DTO> implements Kq03Service {

    @Override
    public QueryWrapper<Kq03Entity> getWrapper(Map<String, Object> params) {
        String devicestatus = (String) params.get("devicestatus");
        String devicekey = (String) params.get("devicekey");
        QueryWrapper<Kq03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(devicestatus), "devicestatus", devicestatus);
        wrapper.eq(StringUtils.isNotBlank(devicekey), "devicekey", devicekey);
        wrapper.orderByAsc("devicestatus");
        return wrapper;
    }


    @Override
    public void saveDeviceCode() {
        //一次生成50个设备注册码
        for (int i = 0; i < 50; i++) {
            String aCase = IdUtil.simpleUUID().toUpperCase();
            String devicemotion = StrUtil.subWithLength(aCase, 0, 16);
            Kq03Entity kq03Entity = new Kq03Entity();
            kq03Entity.setDevicekey(devicemotion);
            baseDao.insert(kq03Entity);
        }
    }

    @Override
    public void validatorDeviceKey(String deviceKey) {
        Integer deviceKeyCount = baseDao.selectByDeviceKey(deviceKey);
        if (deviceKeyCount == 0) {
            throw new RenException("设备注册码不可用");
        }
    }

    @Override
    public void saveDeviceKeyAndDevicesNo(@NotBlank String devicesNo, @NotBlank String deviceKey) {
        Kq03Entity kq03Entity = new Kq03Entity();
        kq03Entity.setDeviceserialno(devicesNo);
        kq03Entity.setDevicekey(deviceKey);
        kq03Entity.setDevicestatus("1");
        baseDao.updateById(kq03Entity);
    }
}