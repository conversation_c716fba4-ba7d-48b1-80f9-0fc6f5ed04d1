package io.renren.modules.enterprise.kq05.dao;


import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq05.dto.Kq05DTO;
import io.renren.modules.enterprise.kq05.dto.Kq05ListDTO;
import io.renren.modules.enterprise.kq05.entity.Kq05Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 考勤人员指令表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-20
 */
@Mapper
public interface Kq05Dao extends BaseDao<Kq05Entity> {

    List<Kq05ListDTO> getKq05Data(Map<String, Object> params);

    List<Kq05DTO> getKq05Detail(Map<String, Object> params);
}