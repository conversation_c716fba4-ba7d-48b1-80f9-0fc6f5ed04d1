package io.renren.modules.enterprise.cd03.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cd03.dto.Cd03InfoDTO;
import io.renren.modules.enterprise.cd03.dto.Cd03PageDTO;
import io.renren.modules.enterprise.cd03.entity.Cd03Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 不良行为记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-09
 */
@Mapper
public interface Cd03Dao extends BaseDao<Cd03Entity> {

    List<Cd03PageDTO> getList(Page<Cd03PageDTO> page, Map<String, Object> params);

    Cd03InfoDTO getCd03(Long id);
}