package io.renren.modules.enterprise.ps04merge.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.pj01info.dto.Ps04PageDTO;
import io.renren.modules.enterprise.ps04merge.dto.MergeManagersDTO;
import io.renren.modules.enterprise.ps04merge.dto.Ps04MergeDTO;
import io.renren.modules.enterprise.ps04merge.dto.Ps04MergeInfoDTO;
import io.renren.modules.enterprise.ps04merge.entity.Ps04MergeEntity;

import java.util.List;
import java.util.Map;

/**
 * 管理人员合并表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-04
 */
public interface Ps04MergeService extends CrudService<Ps04MergeEntity, Ps04MergeDTO> {

    Ps04MergeInfoDTO getInfo(Long id);

    Result getProjects(String pj0101);

    Result<List<Ps04PageDTO>> getProjectManagers(Long pj0101);

    Result mergeManagers(MergeManagersDTO dto);

    PageData<Ps04MergeDTO> getList(Map<String, Object> params);

    Result<PageData<Ps04PageDTO>> getMergeManagerPage(MergeManagersDTO dto);
}