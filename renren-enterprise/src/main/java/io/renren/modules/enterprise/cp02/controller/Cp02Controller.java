package io.renren.modules.enterprise.cp02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.annotation.cachelockvalidator.CacheLock;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.cp02.dto.Cp02DTO;
import io.renren.modules.enterprise.cp02.dto.Cp02ItemsDTO;
import io.renren.modules.enterprise.cp02.dto.Cp02PageDTO;
import io.renren.modules.enterprise.cp02.excel.Cp02Excel;
import io.renren.modules.enterprise.cp02.service.Cp02Service;
import io.renren.modules.enterprise.cp02.vo.Cp02SaveVO;
import io.renren.modules.enterprise.cp02.vo.Cp02UpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@RestController
@RequestMapping("enterprise/cp02")
@Api(tags = "参建单位信息")
public class Cp02Controller {
    @Autowired
    private Cp02Service cp02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:cp02:page")
    public Result<PageData<Cp02PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Cp02PageDTO> page = cp02Service.pageList(params);
        return new Result<PageData<Cp02PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:cp02:info")
    public Result<Cp02DTO> get(@PathVariable("id") Long id) {
        Cp02DTO data = cp02Service.get(id);
        return new Result<Cp02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @CacheLock(prefix = "cp02")
    @RequiresPermissions("enterprise:cp02:save")
    public Result save(@CacheParam @RequestBody Cp02SaveVO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);
        cp02Service.saveCp02Info(dto);
        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @CacheLock(prefix = "cp02")
    @RequiresPermissions("enterprise:cp02:update")
    public Result update(@RequestBody Cp02UpdateVO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);
        cp02Service.updateCp02Info(dto);
        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:cp02:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        cp02Service.delete(ids);

        return new Result();
    }


    @PostMapping("/addCompanyInfo")
    @ApiOperation("添加参建单位")
    @LogOperation("添加参建单位")
//    @CacheLock(prefix = "cp02")
//    @RequiresPermissions("enterprise:cp02:addCompany")
    public Result addCompanyInfo(@RequestBody Cp02SaveVO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);
        cp02Service.addCompanyInfo(dto);
        return new Result();
    }

    @GetMapping("/getCp02Items")
    @ApiOperation("获取当前项目下企业")
    @LogOperation("获取当前项目下企业")
    public Result<List<Cp02ItemsDTO>> getCp02Items() {

        return cp02Service.getCp02Items();
    }


    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:cp02:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Cp02DTO> list = cp02Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Cp02Excel.class);
    }
}