package io.renren.modules.enterprise.ot02.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.ot01.dao.Ot01Dao;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.entity.Ot01Entity;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.enterprise.ot02.dao.Ot02Dao;
import io.renren.modules.enterprise.ot02.dto.Ot02DTO;
import io.renren.modules.enterprise.ot02.dto.Ot02PageDTO;
import io.renren.modules.enterprise.ot02.entity.Ot02Entity;
import io.renren.modules.enterprise.ot02.service.Ot02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 档案上传明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-06-07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ot02ServiceImpl extends CrudServiceImpl<Ot02Dao, Ot02Entity, Ot02DTO> implements Ot02Service {
    @Autowired
    private Ot01Service ot01Service;

    @Autowired
    private Ot01Dao ot01Dao;

    @Override
    public QueryWrapper<Ot02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ot02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ot02PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ot02Entity> page = getPage(params, "", false);
        List<Ot02PageDTO> ot02PageDTOS = baseDao.selectPageList(params);

        return getPageData(ot02PageDTOS, page.getTotal(), Ot02PageDTO.class);
    }

    @Override
    public PageData<Ot02PageDTO> getOt02ListByPj0101(Map<String, Object> params) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        params.put("pj0101", pj0101);
        IPage<Ot02Entity> page = getPage(params, "", false);
        List<Ot02PageDTO> ot02ListByPj0101 = baseDao.getOt02ListByPj0101(params);
        return getPageData(ot02ListByPj0101, page.getTotal(), Ot02PageDTO.class);
    }

    @Override
    public void saveInfo(Ot02DTO dto) {
        // 查找到相对应月份的文件信息
        Date years = dto.getYearMonth();
        String yearMonth = DateUtil.format(years, "yyyyMM");
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        Long getOt0201 = baseDao.getOt02YearMonthAndPj0101(pj0101, yearMonth);
        if (Objects.isNull(getOt0201)) {
            throw new RenException("未查询到当前项目文件信息");
        }
        dto.setOt0201(getOt0201);
        //更新OT01数据
        List<Ot01DTO> ot01DTOList = dto.getOt01DTOList();
        if (ot01DTOList.size() > 0) {
            //过滤出业务id为空的数据
            List<Long> ot0101List = ot01DTOList
                    .stream()
                    .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                    .map(Ot01DTO::getOt0101)
                    .collect(Collectors.toList());
            if (ot0101List.size() > 0) {
                ot01Service.updateBusily(dto.getOt0201(), ot0101List);
            }
        }

    }

//    @Override
//    public List<Ot01DTO> getData(Map<String, Object> params) {
//
//
//        return baseDao.selectFileData(params);
//    }
    @Override
    public List<Ot01DTO> getData(Map<String, Object> params) {
        if (Objects.isNull(params.get("ot0201"))|| StringUtils.isEmpty(params.get("ot0201").toString())) {
            throw new RenException("档期ID不能为空");
        }
        String ot0201 = params.get("ot0201").toString();
        int ot01Count = ot01Dao.selectCount(new QueryWrapper<Ot01Entity>().eq("busisysno",ot0201).eq("busitype","03")).intValue();
        if (ot01Count == 0) {
            throw new RenException("未查询到项目当前月份文件");
        }
        return baseDao.selectFileData(params);
    }
}