package io.renren.modules.enterprise.kq05.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考勤人员指令表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_KQ05")
public class Kq05Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long kq0501;
    /**
     * 人员ID(ps0201或者ps0401)
     */
    private Long userId;
    /**
     * 人员类型(1建筑工人、2项目管理人员)
     */
    private String personType;
    /**
     * 设备类型
     */
    private String terminaltype;
    /**
     * 设备序列号
     */
    private String deviceserialno;

    /**
     * 指令
     */
    private String cmdCode;
    /**
     * 参数
     */
    private String params;
    /**
     * 状态(0待处理，1成功，2失败)
     */
    private String status;
    /**
     * 接口返回的信息
     */
    private String message;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}