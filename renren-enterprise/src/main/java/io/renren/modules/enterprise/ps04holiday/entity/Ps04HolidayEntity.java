package io.renren.modules.enterprise.ps04holiday.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 缺勤报备管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS04_HOLIDAY")
public class Ps04HolidayEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 请假人员ID
     */
    private Long ps0401;
    /**
     * 开始日期
     */
    private Date startdate;
    /**
     * 结束日期
     */
    private Date enddate;
    /**
     * 请假类别
     */
    private String holidaytype;
    /**
     * 请假原因
     */
    private String reson;
    /**
     * 代岗人员ID
     */
    private Long ps0301;
    /**
     * 审核状态
     */
    private String auditstatus;
    /**
     * 审核时间
     */
    private Date auditdate;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核结果
     */
    private String auditresult;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}