package io.renren.modules.enterprise.ps06.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-04-27
 */
@Data
public class BPs06Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps0601;
    @Excel(name = "工人主键ID")
    private BigDecimal ps0201;
    @Excel(name = "进退场时间")
    private Date entryOrExitTime;
    @Excel(name = "进退场状态")
    private String inOrOut;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}