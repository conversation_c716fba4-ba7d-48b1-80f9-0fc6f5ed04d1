package io.renren.modules.enterprise.pa03.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.pa03.dao.Pa03Dao;
import io.renren.modules.enterprise.pa03.dto.Pa03DTO;
import io.renren.modules.enterprise.pa03.entity.Pa03Entity;
import io.renren.modules.enterprise.pa03.service.Pa03Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 工资代发明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-26
 */
@Service
public class Pa03ServiceImpl extends CrudServiceImpl<Pa03Dao, Pa03Entity, Pa03DTO> implements Pa03Service {

    @Override
    public QueryWrapper<Pa03Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pa03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }
    @Override
    public PageData<Pa03DTO> page(Map<String, Object> params) {
        // 分页
        IPage<Pa03Entity> page = getPage(params, "", false);
        // 查询
        params.put("deptId", SecurityUser.getDeptId());
        List<Pa03DTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Pa03DTO.class);
    }

}