package io.renren.modules.enterprise.pj01info.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-04-26 10:52
 */
@Data
@ApiModel(value = "班组分页信息")
public class Tm01PageDTO implements Serializable {
    private static final long serialVersionUID = -5460410451255984864L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "tm0101")
    private Long tm0101;

    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "班组长ID")
    private Long ps0201;

    @ApiModelProperty(value = "所属企业", hidden = true)
    private String corpname;

    @ApiModelProperty(value = "所属项目", hidden = true)
    private String projectName;

    @ApiModelProperty(value = "责任人")
    private String responsiblepersonname;

    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
}
