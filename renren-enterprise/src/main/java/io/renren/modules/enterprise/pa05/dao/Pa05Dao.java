package io.renren.modules.enterprise.pa05.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pa05.dto.Pa05DetailDTO;
import io.renren.modules.enterprise.pa05.dto.Pa05PageDTO;
import io.renren.modules.enterprise.pa05.dto.Pa05SearchPersonDTO;
import io.renren.modules.enterprise.pa05.entity.Pa05Entity;
import io.renren.modules.enterprise.pa06.dto.Pa06DTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 工人工资单
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface Pa05Dao extends BaseDao<Pa05Entity> {


    /**
     * 分页
     * @param params
     * @return
     */
    IPage<Pa05PageDTO> getPageList(@Param("page")IPage page, @Param("params") Map<String, Object> params) ;

    /**
     * 获取人员分页数据
     * @param page
     * @param params
     * @return
     */
    IPage<Pa05SearchPersonDTO> getSearchPageList(@Param("page")IPage page, @Param("params") Map<String, Object> params) ;


    /**
     * pa05详情
     * @param page
     * @param params
     * @return
     */
    IPage<Pa06DTO> detailPage(@Param("page")IPage page, @Param("params") Map<String, Object> params) ;


    /**
     * 删除工资总表数据
     * @param pa0501
     */
    void deletePa05ById(Long pa0501) ;


    /**
     * 详情
     * @param pa0501
     * @return
     */
    Pa05DetailDTO getDetail(Long pa0501) ;
}