package io.renren.modules.enterprise.cp07.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.common.service.CommonService;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.enterprise.cp07.dao.Cp07Dao;
import io.renren.modules.enterprise.cp07.dto.Cp07DTO;
import io.renren.modules.enterprise.cp07.entity.Cp07Entity;
import io.renren.modules.enterprise.cp07.service.Cp07Service;
import io.renren.modules.enterprise.jg07.dto.Jg07DTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 网上提交情况说明表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-20
 */
@Service
public class Cp07ServiceImpl extends CrudServiceImpl<Cp07Dao, Cp07Entity, Cp07DTO> implements Cp07Service {
    @Autowired
    private CommonService commonService;

    @Override
    public QueryWrapper<Cp07Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Cp07Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Cp07DTO> pageList(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        params.put("cp0101", commonService.getUserCp0101());
        Page<Cp07DTO> page = new Page<>(curPage, limit);
        List<Cp07DTO> list = baseDao.getList(page, params);
        return getPageData(list, page.getTotal(), Cp07DTO.class);
    }
}