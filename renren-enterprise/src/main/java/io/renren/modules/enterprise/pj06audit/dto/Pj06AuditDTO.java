package io.renren.modules.enterprise.pj06audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-27
 */
@Data
@ApiModel(value = "${comments}")
public class Pj06AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键id")
	private Long id;
		@ApiModelProperty(value = "项目注册id")
	private Long pj0601;
		@ApiModelProperty(value = "审核状态（0：待审核，1：审核通过，2：审核不通过）")
	private String auditstatus;
		@ApiModelProperty(value = "审核结果")
	private String auditresult;
		@ApiModelProperty(value = "审核时间")
	private Date auditdate;
		@ApiModelProperty(value = "企业id")
	private Long cp0101;
		@ApiModelProperty(value = "参建类型")
	private String corptype;
	@ApiModelProperty(value = "项目名称")
	private String name;
	@ApiModelProperty(value = "联系人")
	private String linkman;
	@ApiModelProperty(value = "联系电话")
	private String linkphone;
	@ApiModelProperty(value = "项目所在地")
	private String areacode;


}