package io.renren.modules.enterprise.cp04audit.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分包信息审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-02
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_CP04_AUDIT")
public class Cp04AuditEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long cp0401;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 分包单位ID
     */
	private Long cp0201;
    /**
     * 分包工程名称
     */
	private String subcontractname;
    /**
     * 分包工程内容
     */
	private String subcontractcontent;
    /**
     * 开工时间
     */
	private Date startdate;
    /**
     * 计划完工时间
     */
	private Date completedate;
    /**
     * 分包价格（万元）
     */
	private BigDecimal subcontractprice;
    /**
     * 备注
     */
	private String memo;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
	private String auditstatus;
    /**
     * 审核人
     */
	private String auditor;
    /**
     * 审核时间
     */
	private Date auditdate;
    /**
     * 审核结果
     */
	private String auditresult;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}