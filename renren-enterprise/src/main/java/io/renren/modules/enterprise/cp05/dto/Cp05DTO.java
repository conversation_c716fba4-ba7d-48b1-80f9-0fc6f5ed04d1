package io.renren.modules.enterprise.cp05.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;
import java.util.List;

/**
 * 企业担保管理表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-15
 */
@Data
@ApiModel(value = "企业担保管理表")
public class Cp05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "主键")
	private Long cp0501;
	@ApiModelProperty(value = "申请单单号")
	private String applicationno;
	@ApiModelProperty(value = "保证金金额")
	private BigDecimal bondamount;
	@ApiModelProperty(value = "保证金类型")
	private String bondtype;
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	@ApiModelProperty(value = "企业id")
	private Long cp0101;
	@ApiModelProperty(value = "项目id")
	private Long pj0101;
	@ApiModelProperty(value = "申请时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date applytime;
	@ApiModelProperty(value = "是否退还")
	private String isrefund;
	@ApiModelProperty(value = "审核状态")
	private String auditstatus;
	@ApiModelProperty(value = "证明材料")
	private List<Ot01DTO> evidences;
	@ApiModelProperty(value = "竣工备案")
	private List<Ot01DTO> completionFilings;
	@ApiModelProperty(value = "决算备案")
	private List<Ot01DTO> countingFilings;
	
}