package io.renren.modules.enterprise.kq05.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-11-10 15:35
 */

@Data
@ApiModel(value = "考勤人员设备交互对象")
public class PersonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员ID")
    private Long userId;

    @ApiModelProperty(value = "人员姓名")
    private String name;

    @ApiModelProperty(value = "人员照片路径")
    private String imageUrl;

    @ApiModelProperty(value = "人员类型")
    private String personType;

    @ApiModelProperty(value = "设备序列号")
    private String devicemotion;

    @ApiModelProperty(value = "设备类型")
    private String terminalType;
}
