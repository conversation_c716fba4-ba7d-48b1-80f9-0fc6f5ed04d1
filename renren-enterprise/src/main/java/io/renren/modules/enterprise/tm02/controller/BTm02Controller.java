package io.renren.modules.enterprise.tm02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.tm02.dto.BTm02DTO;
import io.renren.modules.enterprise.tm02.service.BTm02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 班组进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
@RestController
@RequestMapping("enterprise/btm02")
@Api(tags = "班组进退场信息")
public class BTm02Controller {
    @Autowired
    private BTm02Service bTm02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:btm02:page")
    public Result<PageData<BTm02DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<BTm02DTO> page = bTm02Service.page(params);
        return new Result<PageData<BTm02DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:btm02:info")
    public Result<BTm02DTO> get(@PathVariable("id") Long id) {
        BTm02DTO data = bTm02Service.get(id);

        return new Result<BTm02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:btm02:save")
    public Result save(@RequestBody BTm02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bTm02Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:btm02:update")
    public Result update(@RequestBody BTm02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bTm02Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:btm02:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        bTm02Service.delete(ids);

        return new Result();
    }

}