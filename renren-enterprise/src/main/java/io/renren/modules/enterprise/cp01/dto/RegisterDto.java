package io.renren.modules.enterprise.cp01.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.renren.common.annotation.ismobilevalidator.IsMobileValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "企业注册")
public class RegisterDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "验证码")
    @NotBlank(message="验证码不能为空")
    private String verifyCode;

    @ApiModelProperty(value = "附件id")
    private List<String> ot0101;

    @ApiModelProperty(value = "企业信息")
    @Valid
    private Cp01DTO cp01DTO;

}
