package io.renren.modules.enterprise.pa05.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chris
 * @Date : 2023-01-03
 **/
@Data
public class Pa05CreatePayrollVO {

    @NotBlank(message = "请选择一个班组生成")
    @ApiModelProperty(value = "生成年月（yyyyMM）")
    private String tm0101;

    @NotBlank(message = "请选择生成年月")
    @ApiModelProperty(value = "生成年月（yyyyMM）")
    private String createDate;

    @ApiModelProperty(value = "生成人员")
    @NotNull(message = "请选择至少一条人员数据进行操作")
    private List<String> users;
}
