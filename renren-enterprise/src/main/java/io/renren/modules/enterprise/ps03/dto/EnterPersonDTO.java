package io.renren.modules.enterprise.ps03.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "管理人员入场")
public class EnterPersonDTO {

    @ApiModelProperty(value = "人员ID")
    private Long ps0301;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "岗位类型")
    private Long jobtype;
    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;
    @ApiModelProperty(value = "附件id")
    private List<Ot01DTO> certificateFiles;
}
