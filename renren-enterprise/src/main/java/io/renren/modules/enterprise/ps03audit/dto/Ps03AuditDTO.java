package io.renren.modules.enterprise.ps03audit.dto;

import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;
import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-28
 */
@Data
@ApiModel(value = "${comments}")
public class Ps03AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键id")
	private Long ps0301;
		@ApiModelProperty(value = "当前项目id")
	private Long pj0101;
		@ApiModelProperty(value = "当前项目名称")
	private String projectName;
		@ApiModelProperty(value = "在职企业id")
	private Long cp0101;
		@ApiModelProperty(value = "人员id")
	private Long ps0101;
		@ApiModelProperty(value = "在职状态")
	private String managestatus;
		@ApiModelProperty(value = "头像采集照片")
	private String photo;
		@ApiModelProperty(value = "是否购买工伤或意外伤害保险")
	private String hasbuyinsurance;
		@ApiModelProperty(value = "入职时间")
	private Date inductiontime;
		@ApiModelProperty(value = "离职时间")
	private Date departuretime;
		@ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
	private String auditstatus;
		@ApiModelProperty(value = "审核人")
	private String auditor;
		@ApiModelProperty(value = "审核时间")
	private Date auditdate;
		@ApiModelProperty(value = "审核结果")
	private String auditresult;
	@ApiModelProperty(value = "证书")
	private List<Ot01DTO> certificateFiles;
	@ApiModelProperty(value = "社保")
	private List<Ot01DTO> insuranceFiles;
	@ApiModelProperty(value = "人员基础信息")
	@Valid
	private Ps01DTO ps01DTO;

}