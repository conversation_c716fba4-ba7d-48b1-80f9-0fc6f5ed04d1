package io.renren.modules.enterprise.pj02.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.modules.enterprise.pj02.dto.Pj02DTO;
import io.renren.modules.enterprise.pj02.entity.Pj02Entity;

/**
 * 项目施工许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Pj02Service extends IService<Pj02Entity> {
    /**
     * 根据项目ID查询施工许可证信息
     * @param id
     * @return
     */
    Pj02DTO getByPj0101(Long id);
}