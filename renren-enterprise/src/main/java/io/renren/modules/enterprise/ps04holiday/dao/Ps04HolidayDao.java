package io.renren.modules.enterprise.ps04holiday.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps04holiday.dto.Ps04HolidayDTO;
import io.renren.modules.enterprise.ps04holiday.entity.Ps04HolidayEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Mapper
public interface Ps04HolidayDao extends BaseDao<Ps04HolidayEntity> {
    List<Ps04HolidayDTO> pageList(Map<String, Object> params);

    Ps04HolidayDTO selectInfoById(Long id);
}