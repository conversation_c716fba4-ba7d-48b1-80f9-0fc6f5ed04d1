<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj06.dao.Pj06Dao">
    <resultMap type="io.renren.modules.enterprise.pj06.entity.Pj06Entity" id="pj06Map">
        <result property="pj0601" column="PJ0601"/>
        <result property="name" column="NAME"/>
        <result property="industry" column="INDUSTRY"/>
        <result property="areacode" column="AREACODE"/>
        <result property="linkman" column="LINKMAN"/>
        <result property="linkphone" column="LINKPHONE"/>
        <result property="constructionname" column="CONSTRUCTIONNAME"/>
        <result property="constructionnumber" column="CONSTRUCTIONNUMBER"/>
        <result property="contractname" column="CONTRACTNAME"/>
        <result property="contractnumber" column="CONTRACTNUMBER"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="virareacode" column="VIRAREACODE"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <!-- 查询未审核的项目数量   -->
    <select id="getProjectNumber" resultType="java.lang.Long">
        select count(0)
        from B_PJ06 t
        where t.NAME = #{name}
          and t.auditstatus = '0'
    </select>
    <!--  查询已存在的项目数量  -->
    <select id="getPj01Number" resultType="java.lang.Long">
        select count(1)
        from B_PJ01 t
        where t.NAME = #{name}
    </select>
    <select id="selectUserName" resultType="java.lang.Long">
        select seq_b_username.nextval from dual
    </select>
    <select id="getInfo" resultType="io.renren.modules.enterprise.pj06.dto.Pj06DTO">
            select (select a.corpname || '—' || a.corpcode || '—' || y.dict_label || '(' ||
                       to_char(b.auditdate, 'yyyy-MM-dd hh:mi:ss') || ')'
                  from b_cp01 a, b_pj06_audit b, sys_dict_type x, sys_dict_data y
                 where a.cp0101 = b.cp0101
                   and b.pj0601 = t.pj0601
                   and b.corptype = 8
                   and x.id = y.dict_type_id
                   and x.dict_type = 'AUDITSTATUS'
                   and b.auditstatus = y.dict_value) as constructionname,
               (select a.corpname || '—' || a.corpcode || '—' || y.dict_label || '(' ||
                       to_char(b.auditdate, 'yyyy-MM-dd hh:mi:ss') || ')'
                  from b_cp01 a, b_pj06_audit b, sys_dict_type x, sys_dict_data y
                 where a.cp0101 = b.cp0101
                   and b.pj0601 = t.pj0601
                   and b.corptype = 9
                   and x.id = y.dict_type_id
                   and x.dict_type = 'AUDITSTATUS'
                   and b.auditstatus = y.dict_value) as contractname,
               (select a.corpname || '—' || a.corpcode || '—' || y.dict_label || '(' ||
                       to_char(b.auditdate, 'yyyy-MM-dd hh:mi:ss') || ')'
                  from b_cp01 a, b_pj06_audit b, sys_dict_type x, sys_dict_data y
                 where a.cp0101 = b.cp0101
                   and b.pj0601 = t.pj0601
                   and b.corptype = 7
                   and x.id = y.dict_type_id
                   and x.dict_type = 'AUDITSTATUS'
                   and b.auditstatus = y.dict_value) as monitorname,
               t.*
          from B_PJ06 t
         where t.pj0601 = #{pj0601}
    </select>
</mapper>