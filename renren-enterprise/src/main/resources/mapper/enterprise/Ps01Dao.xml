<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps01.dao.Ps01Dao">
    <select id="loadPs01" resultType="io.renren.modules.enterprise.ps01.dto.Ps01DTO">
        select *
        from B_PS01 t
        where t.IDCARDNUMBER = #{idCardNumber}
    </select>
    <select id="getList" resultType="io.renren.modules.enterprise.ps01.dto.Ps01DTO">
        select t.PS0101,
        t.NAME,
        t.IDCARDTYPE,
        t.IDCARDNUMBER,
        t.GENDER,
        t.NATION,
        t.ADDRESS,
        t.URGENTLINKMAN,
        t.URGENTLINKMANPHONE,
        t.CELLPHONE,
        x.ps0201
        from B_PS01 t
        left join b_ps02 x
        on t.ps0101 = x.ps0101
        and x.pj0101 = #{pj0101}
        where 1 = 1
        <if test="name != null and name != ''">
            and t.NAME like '%' || #{name} || '%'
        </if>
        <if test="idcardnumber != null and idcardnumber != ''">
            and t.IDCARDNUMBER like '%' || #{idcardnumber} || '%'
        </if>
    </select>

    <select id="getPersonInfo" resultType="io.renren.modules.enterprise.ps01.dto.PersonInfoDTO">
        select ps0101,
               name,
               idcardtype,
               idcardnumber,
               gender,
               nation,
               birthday,
               address,
               edulevel,
               degree,
               workertype,
               areacode,
               headimageurl,
               politicstype,
               isjoined,
               joinedtime,
               cellphone,
               cultureleveltype,
               specialty,
               hasbadmedicalhistory,
               urgentlinkman,
               urgentlinkmanphone,
               workdate,
               maritalstatus,
               grantorg,
               positiveidcardimageurl,
               negativeidcardimageurl,
               startdate,
               expirydate
        from b_ps01
        where idcardnumber = #{idcardnumber}

    </select>

    <select id="getUserImage" resultType="io.renren.modules.enterprise.ps01.entity.Ps01Entity">
        select * from (select ps0101, headimageurl from b_ps01 where headimageurl like 'https://ybyc.hgyun.net:99/carp/file/k/q/open/image/%') where rownum &lt;= 2000
    </select>
</mapper>