<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps03audit.dao.Ps03AuditDao">

    <resultMap type="io.renren.modules.enterprise.ps03audit.entity.Ps03AuditEntity" id="ps03AuditMap">
        <result property="ps0301" column="PS0301"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0101" column="CP0101"/>
        <result property="ps0101" column="PS0101"/>
        <result property="managestatus" column="MANAGESTATUS"/>
        <result property="photo" column="PHOTO"/>
        <result property="hasbuyinsurance" column="HASBUYINSURANCE"/>
        <result property="inductiontime" column="INDUCTIONTIME"/>
        <result property="departuretime" column="DEPARTURETIME"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditresult" column="AUDITRESULT"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.enterprise.ps03audit.dto.Ps03AuditPageDTO">
        select t.ps0301,
        a.name,
        a.idcardnumber,
        a.gender,
        a.cellphone,
        t.managestatus,
        t.auditstatus,
        x.pj0101,
        x.name projectname,
        y.in_or_out inOrOut,
        y.jobtype
        from B_PS03_audit t, b_ps01 a, b_pj01 x, b_ps04_audit y
        where t.ps0101 = a.ps0101(+)
        and t.pj0101 = x.pj0101(+)
        and t.ps0301 = y.ps0301(+)
        and t.pj0101 = y.pj0101(+)
        and t.managestatus  = '1'
        and t.cp0101 = #{params.cp0101}
        <if test="name != null and name.trim() != ''">
            and a.name like '%' || #{name} || '%'
        </if>
        <if test="jobtype != null and jobtype.trim() != ''">
            and y.jobtype = #{jobtype}
        </if>
        <if test="projectName != null and projectName.trim() != ''">
            and x.name like '%' || #{projectName} || '%'
        </if>
    </select>

</mapper>