<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps04holiday.dao.Ps04HolidayDao">
    <select id="pageList" resultType="io.renren.modules.enterprise.ps04holiday.dto.Ps04HolidayDTO">
        select t.*,
               ceil(t.enddate - t.startdate) as holidays,
               a.name as projectname,
               h.areacode,
               d.name,
               d.ps0101,
               (select x.name from b_ps01 x,b_ps03 y where x.ps0101=y.ps0101 and y.ps0301=t.ps0301) as newname,
               (select x.ps0101 from b_ps03 x where x.ps0301=t.ps0301) as newps0101,
               g.corpname,
               b.jobtype
          from B_PS04_HOLIDAY t,
               b_pj01         a,
               b_ps04         b,
               b_ps03         c,
               b_ps01         d,
               r_pj01_dept    e,
               b_cp02         f,
               b_cp01         g,
               sys_dept       h
         where t.pj0101 = a.pj0101
           and t.ps0401 = b.ps0401
           and b.ps0301 = c.ps0301
           and c.ps0101 = d.ps0101
           and b.cp0201 = f.cp0201
           and f.cp0101 = g.cp0101
           and a.dept_id = h.id
           and t.pj0101 = e.pj0101
           and e.dept_id = #{deptId}
        <if test="name != null and name != ''">
            and d.name like '%'||#{name}||'%'
        </if>
        <if test="projectname != null and projectname != ''">
            and a.name like '%'||#{projectname}||'%'
        </if>
        <if test="auditstatus != null and auditstatus != ''">
            and t.auditstatus = #{auditstatus}
        </if>
        <if test="areacode != null and areacode != ''">
            and h.areacode = #{areacode}
        </if>
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.enterprise.ps04holiday.dto.Ps04HolidayDTO">
        select a.pj0101,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.cp0201 = b.cp0201) as corpname,
               b.jobtype,
               t.*,
               ceil(t.enddate - t.startdate) as holidays
          from B_PS04_HOLIDAY t, b_pj01 a, b_ps04 b
         where t.pj0101 = a.pj0101
           and t.ps0401 = b.ps0401
           and t.id = #{id}
    </select>
</mapper>
