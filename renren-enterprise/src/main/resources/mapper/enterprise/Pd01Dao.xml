<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pd01.dao.Pd01Dao">

    <resultMap type="io.renren.modules.enterprise.pd01.entity.Pd01Entity" id="pd01Map">
        <result property="pd0101" column="PD0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tagno" column="TAGNO"/>
        <result property="model" column="MODEL"/>
        <result property="devicename" column="DEVICENAME"/>
        <result property="trademark" column="TRADEMARK"/>
        <result property="devicetype" column="DEVICETYPE"/>
        <result property="dischargestage" column="DISCHARGESTAGE"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="enginetype" column="ENGINETYPE"/>
        <result property="factorymonthly" column="FACTORYMONTHLY"/>
        <result property="propertyunit" column="PROPERTYUNIT"/>
        <result property="creditcode" column="CREDITCODE"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getList" resultType="io.renren.modules.enterprise.pd01.dto.Pd01DTO">
        select t.PD0101,t.TAGNO,t.MODEL,t.DEVICENAME,t.DEVICETYPE,
        t.DISCHARGESTAGE,t.MANUFACTURER,t.PROPERTYUNIT
        from B_PD01 t,R_PJ01_DEPT a
        where t.PJ0101=a.PJ0101 and a.DEPT_ID=#{deptId}
        <if test="devicename != null and devicename.trim() != ''">
            and DEVICENAME like #{devicename}
        </if>
    </select>
    <select id="loadEquipmentInfo" resultType="io.renren.common.common.service.dto.CommonDto">
        select t.PD0101 value, t.DEVICENAME label
        from B_PD01 t,
             R_PJ01_DEPT a
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
    </select>


</mapper>