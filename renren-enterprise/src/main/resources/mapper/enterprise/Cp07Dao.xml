<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp07.dao.Cp07Dao">

    <resultMap type="io.renren.modules.enterprise.cp07.entity.Cp07Entity" id="cp07Map">
        <result property="cp0701" column="CP0701"/>
        <result property="cp0101" column="CP0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="title" column="TITLE"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="reply" column="REPLY"/>
        <result property="replyer" column="REPLYER"/>
        <result property="replydate" column="REPLYDATE"/>
        <result property="replystatus" column="REPLYSTATUS"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.enterprise.cp07.dto.Cp07DTO">
        select t1.*, t2.name projectName
        from B_CP07 t1, B_PJ01 t2 where t1.pj0101 = t2.pj0101 and t1.cp0101 = #{params.cp0101}
    </select>


</mapper>