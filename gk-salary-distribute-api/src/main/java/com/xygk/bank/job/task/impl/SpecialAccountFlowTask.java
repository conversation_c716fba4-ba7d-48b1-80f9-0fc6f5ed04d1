package com.xygk.bank.job.task.impl;

import com.xygk.bank.business.service.WTa07Service;
import com.xygk.bank.job.annotation.GKScheduled;
import com.xygk.bank.job.task.ITask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: CJF
 * @CreateTime: 2025-07-02  10:56
 * @Description: 生成专户流水查询任务
 * @Version: 1.0
 */
//@Component
@GKScheduled(
        scheduledName = "specialAccountFlowTask",
        beanName = "specialAccountFlowTask",
        params = "",
        cronExpression = "0 23 * * 0",
        status = 1
)
public class SpecialAccountFlowTask implements ITask {
    @Autowired
    private WTa07Service wTa07Service;
    @Override
    public void run(String params) {
        wTa07Service.generateSpecialAccountFlowTask();
    }
}
