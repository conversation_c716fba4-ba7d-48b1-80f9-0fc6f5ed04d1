package com.xygk.bank.business.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 专户余额查询记录
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("W_TA09")
public class WTa09Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ta0901;
    /**
     * 任务ID
     */
	private Long ta0101;
    /**
     * 专户户名
     */
	private String ta0902;
    /**
     * 专户账号
     */
	private String ta0903;
    /**
     * 查询状态
     */
	private String ta0904;
    /**
     * 余额
     */
	private BigDecimal ta0905;
    /**
     * 查询时间
     */
	private Date ta0906;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}