package com.xygk.bank.business.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 专户流水查询记录
 *
 * <AUTHOR>
 * @since 1.0.0 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("W_TA07")
public class WTa07Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long ta0701;
    /**
     * 任务ID
     */
    private Long ta0101;
    /**
     * 专户户名
     */
    private String ta0702;
    /**
     * 专户账号
     */
    private String ta0703;
    /**
     * 查询开始日期
     */
    private Date ta0704;
    /**
     * 查询结束日期
     */
    private Date ta0705;
    /**
     * 反馈状态
     */
    private String ta0706;
    /**
     * 反馈时间
     */
    private Date ta0707;
    /**
     * 专户ID
     */
    private Long pa0101;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}