package com.xygk.bank.business.service;

import com.xygk.bank.business.dto.WOg01DTO;
import com.xygk.bank.business.entity.WOg01Entity;
import io.renren.common.service.CrudService;

import java.util.List;

/**
 * 接入金融机构表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-29
 */
public interface WOg01Service extends CrudService<WOg01Entity, WOg01DTO> {
    /**
     * @description: 查询所有机构密钥
     * @author: CJF
     * @date: 2025/5/29 13:31
     * @param:
     * @return:
     **/
    List<WOg01Entity> selectAll();
}