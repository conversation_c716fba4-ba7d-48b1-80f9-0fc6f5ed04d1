package com.xygk.bank.business.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工资专户流水
 *
 * <AUTHOR>
 * @since 1.0.0 2025-06-10
 */
@Data
@ApiModel(value = "工资专户流水")
public class WTa08DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ta0801;
    @ApiModelProperty(value = "专户流水查询记录ID")
    private Long ta0701;
    @ApiModelProperty(value = "专户户名")
    private String ta0802;
    @ApiModelProperty(value = "专户账号")
    private String ta0803;
    @ApiModelProperty(value = "交易流水号")
    private String ta0804;
    @ApiModelProperty(value = "代发记录ID")
    private Long ta0501;
    @ApiModelProperty(value = "收支方式")
    private String ta0805;
    @ApiModelProperty(value = "交易金额")
    private BigDecimal ta0806;
    @ApiModelProperty(value = "交易类型")
    private String ta0807;
    @ApiModelProperty(value = "交易时间")
    private Date ta0808;
    @ApiModelProperty(value = "是否成功")
    private String ta0809;
    @ApiModelProperty(value = "备注")
    private String ta0810;

}