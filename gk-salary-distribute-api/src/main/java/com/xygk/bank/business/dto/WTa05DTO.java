package com.xygk.bank.business.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工资代发记录
 *
 * <AUTHOR>
 * @since 1.0.0 2025-06-09
 */
@Data
@ApiModel(value = "工资代发记录")
public class WTa05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ta0501;
    @ApiModelProperty(value = "任务ID")
    private Long ta0101;
    @ApiModelProperty(value = "项目名称")
    private String ta0502;
    @ApiModelProperty(value = "所属区域")
    private String ta0503;
    @ApiModelProperty(value = "开户企业名称")
    private String ta0504;
    @ApiModelProperty(value = "开户企业统一社会信用代码")
    private String ta0505;
    @ApiModelProperty(value = "联系人")
    private String ta0506;
    @ApiModelProperty(value = "联系人手机号")
    private String ta0507;
    @ApiModelProperty(value = "专户户名")
    private String ta0508;
    @ApiModelProperty(value = "专户账号")
    private String ta0509;
    @ApiModelProperty(value = "支付方式")
    private String ta0510;
    @ApiModelProperty(value = "工资年月")
    private String ta0511;
    @ApiModelProperty(value = "应发总金额")
    private BigDecimal ta0512;
    @ApiModelProperty(value = "应发总笔数")
    private Short ta0513;
    @JSONField(serialize = false)
    @ApiModelProperty(value = "处理状态")
    private String ta0514;
    @JSONField(serialize = false)
    @ApiModelProperty(value = "代发成功总金额")
    private BigDecimal ta0515;
    @JSONField(serialize = false)
    @ApiModelProperty(value = "代发成功总笔数")
    private Integer ta0516;

}