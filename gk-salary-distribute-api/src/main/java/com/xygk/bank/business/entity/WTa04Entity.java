package com.xygk.bank.business.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资卡核验记录
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("W_TA04")
public class WTa04Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ta0401;
    /**
     * 任务ID
     */
	private Long ta0101;
    /**
     * 工资卡卡号
     */
	private String ta0402;
    /**
     * 姓名
     */
	private String ta0403;
    /**
     * 证件号码
     */
	private String ta0404;
    /**
     * 核验状态
     */
	private String ta0405;
    /**
     * 核验结果备注
     */
	private String ta0406;
	/**
	 * 工人ID
	 */
	private Long ps0201;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}