package com.xygk.bank.business.service;

import com.xygk.bank.business.dto.SpecialaccountFlowDTO;
import com.xygk.bank.business.dto.WTa07DTO;
import com.xygk.bank.business.entity.WTa07Entity;
import io.renren.common.service.CrudService;

import java.util.List;

/**
 * 专户流水查询记录
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-10
 */
public interface WTa07Service extends CrudService<WTa07Entity, WTa07DTO> {
    /**
     * @description: 上传工资专户流水
     * @author: CJF
     * @date: 2025/6/10 15:30
     * @param:
     * @return:
     **/
    void uploadSpecialaccountFlow(SpecialaccountFlowDTO specialaccountFlowDTO);
    /**
     * @description: 生成专户流水查询任务
     * @author: CJF
     * @date: 2025/7/2 10:59
     * @param:
     * @return:
     **/
    void generateSpecialAccountFlowTask();
}