package com.xygk.bank.business.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 接入金融机构表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-05-29
 */
@Data
@ApiModel(value = "接入金融机构表")
public class WOg01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long og0101;
    @ApiModelProperty(value = "机构代码")
    private String og0102;
    @ApiModelProperty(value = "机构名称")
    private String og0103;
    @ApiModelProperty(value = "SM2平台私钥")
    private String og0104;
    @ApiModelProperty(value = "SM2平台公钥")
    private String og0105;
    @ApiModelProperty(value = "SM2对端私钥")
    private String og0106;
    @ApiModelProperty(value = "SM2对端公钥")
    private String og0107;
    @ApiModelProperty(value = "SM3密钥")
    private String og0108;
    @ApiModelProperty(value = "接口根路径")
    private String og0109;
}