package io.renren.modules.earlyWar.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/27 13:41
 */
@Data
@ApiModel(value = "预警处置")
public class EarlyWarningDisposal implements Serializable {
    private static final long serialVersionUID = 6624853872471090032L;

    @ApiModelProperty(value = "预警ID")
    private String jg0101;

    @ApiModelProperty(value = "预警处置动作 1-通过，2-驳回")
    private String dealStatus;

    @ApiModelProperty(value = "预警处置行为 如：通过/预警未消除驳回")
    private String dealInfo;

}
