package io.renren.modules.regul.pj03.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤时长统计
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "项目培训统计")
public class Training1StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String projectname;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
     @ApiModelProperty(value = "主管部门")
    private String virareacode;
    @ApiModelProperty(value = "施工单位")
    private String corpname;
    @ApiModelProperty(value = "项目人数")
    private String workers;
    @ApiModelProperty(value = "培训人数")
    private String trainingworkers;
    @ApiModelProperty(value = "培训率")
    private String trainingpercent;
}