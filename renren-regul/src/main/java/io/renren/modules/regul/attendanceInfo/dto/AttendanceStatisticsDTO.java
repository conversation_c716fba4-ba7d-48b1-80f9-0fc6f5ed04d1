package io.renren.modules.regul.attendanceInfo.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 考勤统计DTO
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
@Data
@ApiModel(value = "考勤统计")
public class AttendanceStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 544936274923486526L;
    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 动态日期数据
     * Key: 日期（格式：DAY_01, DAY_02...）
     * Value: 考勤状态或工时数据
     */
    private Map<String, Object> dailyData = new LinkedHashMap<>();

    /**
     * 月度统计汇总
     */
    private AttendanceSummary summary = new AttendanceSummary();

    /**
     * 月度统计汇总内部类
     */
    @Data
    @ApiModel(value = "月度统计汇总")
    public static class AttendanceSummary implements Serializable {
        private static final long serialVersionUID = 614094039599054234L;
        /**
         * 出勤天数
         */
        private Integer attendanceDays = 0;

        /**
         * 缺勤天数
         */
        private Integer absentDays = 0;

        /**
         * 总工时
         */
        private BigDecimal totalWorkHours = BigDecimal.ZERO;

        /**
         * 平均工时
         */
        private BigDecimal averageWorkHours = BigDecimal.ZERO;

        /**
         * 出勤率
         */
        private BigDecimal attendanceRate = BigDecimal.ZERO;
    }

    /**
     * 添加日期数据
     *
     * @param day   日期（1-31）
     * @param value 数据值
     */
    public void addDailyData(int day, Object value) {
        String key = "DAY_" + String.format("%02d", day);
        this.dailyData.put(key, value);
    }

    /**
     * 获取指定日期的数据
     *
     * @param day 日期（1-31）
     * @return 数据值
     */
    public Object getDailyData(int day) {
        String key = "DAY_" + String.format("%02d", day);
        return this.dailyData.get(key);
    }

    /**
     * 计算统计汇总
     *
     * @param totalDaysInMonth 该月总天数
     */
    public void calculateSummary(int totalDaysInMonth) {
        int attendanceDays = 0;
        BigDecimal totalHours = BigDecimal.ZERO;

        for (Object value : dailyData.values()) {
            if (value != null) {
                String strValue = value.toString();
                if (strValue.startsWith("√")) {
                    attendanceDays++;
                    // 提取工时数据
                    String hoursStr = strValue.substring(1);
                    try {
                        BigDecimal hours = new BigDecimal(hoursStr);
                        totalHours = totalHours.add(hours);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
            }
        }

        this.summary.setAttendanceDays(attendanceDays);
        this.summary.setAbsentDays(totalDaysInMonth - attendanceDays);
        this.summary.setTotalWorkHours(totalHours);

        // 计算平均工时
        if (attendanceDays > 0) {
            this.summary.setAverageWorkHours(
                totalHours.divide(new BigDecimal(attendanceDays), 2, BigDecimal.ROUND_HALF_UP)
            );
        }

        // 计算出勤率
        if (totalDaysInMonth > 0) {
            BigDecimal rate = new BigDecimal(attendanceDays)
                .divide(new BigDecimal(totalDaysInMonth), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100));
            this.summary.setAttendanceRate(rate);
        }
    }
}
