package io.renren.modules.regul.pa03.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.dao.BaseDao;
import io.renren.modules.regul.pa03.dto.Pa03DTO;
import io.renren.modules.regul.pa03.dto.Pa03PageDTO;
import io.renren.modules.regul.pa03.entity.Pa03Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工资代发明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-21
 */
@Mapper
public interface Pa03Dao extends BaseDao<Pa03Entity> {

    List<Pa03DTO> pageList(Map<String, Object> params);

    /**
     * 获取流水明细分页数据
     *
     * @param params
     * @return
     */
    IPage<Pa03PageDTO> pageListData(@Param("page") IPage page, @Param("params") Map<String, Object> params);
}