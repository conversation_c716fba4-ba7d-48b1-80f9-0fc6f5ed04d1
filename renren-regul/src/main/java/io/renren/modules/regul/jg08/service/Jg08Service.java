package io.renren.modules.regul.jg08.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.regul.jg08.dto.Jg08DTO;
import io.renren.modules.regul.jg08.entity.Jg08Entity;
import io.renren.modules.regul.jg09.dto.Jg09DTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工资投诉表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-16
 */
public interface Jg08Service extends CrudService<Jg08Entity, Jg08DTO> {

    PageData<Jg08DTO> pageList(Map<String, Object> params);

    Result getInfo(Map<String, Object> params);

    PageData<Jg09DTO> jg09PageList(Map<String, Object> params);

    Result saveInfo(Jg08DTO dto);

    Result saveProcess(Jg09DTO dto);

    List<DictDTO> getProjectList();

    void export(Map<String, Object> params, HttpServletResponse response);
}