package io.renren.modules.regul.ps04.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.regul.pj01.dto.Kq02PageDTO;
import io.renren.modules.regul.ps04.dto.*;
import io.renren.modules.regul.ps04.entity.Ps04Entity;

import java.util.List;
import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps04Service extends CrudService<Ps04Entity, Ps04DTO> {
    /**
     * 分页查询数据
     *
     * @param params
     * @return
     */
    PageData<Ps04PageDTO> ps04Page(Map<String, Object> params);

    /**
     * 审核列表
     * @param params
     * @return
     */
    PageData<Ps04AuditDTO> auditPageList(Map<String, Object> params);

    /**
     * 审核信息
     * @param params
     * @return
     */
    Ps04AuditDTO getInfo(Map<String, Object> params);

    /**
     * 审核操作
     * @param dto
     */
    void audit(Ps04AuditDTO dto);

    /**
     * 管理人员详情
     * @param params
     * @return
     */
    Ps04DTO getManagerInfo(Map<String, Object> params);

    /**
     * 管理人员从业记录
     * @param params
     * @return
     */
    PageData<Ps04DTO> empRecordPage(Map<String, Object> params);

    PageData<Kq02PageDTO> attendancePageList(Map<String, Object> params);

    PageData<Ps04ConfirmDTO> confirmPageList(Map<String, Object> params);

    void confirm(Ps04PageDTO dto);
}