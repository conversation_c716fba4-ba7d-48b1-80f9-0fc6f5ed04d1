package io.renren.modules.regul.pa02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.regul.pa02.dto.Pa02DTO;
import io.renren.modules.regul.pa02.dto.Pa02PageDTO;
import io.renren.modules.regul.pa02.entity.Pa02Entity;

import java.util.Map;

/**
 * 项目专户流水表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-21
 */
public interface Pa02Service extends CrudService<Pa02Entity, Pa02DTO> {
    /**
     * 数据查询
     * @param params
     * @return
     */
    PageData<Pa02DTO> page(Map<String, Object> params);
}