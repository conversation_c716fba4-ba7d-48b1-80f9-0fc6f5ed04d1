package io.renren.modules.regul.pw02.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
@ApiModel(value = "工资导入明细表")
public class Pw02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键")
	private Long pw0201;
		@ApiModelProperty(value = "导入记录id")
	private Long pw0101;
		@ApiModelProperty(value = "人员ID")
	private Long ps0201;
		@ApiModelProperty(value = "考勤天数")
	private Integer kqts;
		@ApiModelProperty(value = "应发工资")
	private double yfgz;
		@ApiModelProperty(value = "银行卡号")
	private String kh;
	@ApiModelProperty(value = "开户行")
	private String khh;
		@ApiModelProperty(value = "实发工资")
	private double sfgz;
		@ApiModelProperty(value = "系统考勤天数")
	private Integer xtkqts;
	@ApiModelProperty(value = "差异")
	private Integer cy;
		@ApiModelProperty(value = "工人身份证号")
	private String idcardnumber;
		@ApiModelProperty(value = "工人姓名")
	private String name;
		@ApiModelProperty(value = "项目id")
	private Long pj0101;
		@ApiModelProperty(value = "工资所属年月")
	private Long month;
		@ApiModelProperty(value = "工人手机号")
	private String mobile;
		@ApiModelProperty(value = "补录说明")
	private String reason;
		@ApiModelProperty(value = "审核状态（0：待审核，1：通过，2：不通过）")
	private String auditstatus;
		@ApiModelProperty(value = "导入状态（0：完全匹配，1：补录，2：异常）")
	private String importstatus;
		@ApiModelProperty(value = "发放状态（1：成功，2：失败）")
	private String status;

}