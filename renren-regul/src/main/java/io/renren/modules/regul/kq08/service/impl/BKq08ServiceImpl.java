package io.renren.modules.regul.kq08.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.regul.kq08.dao.BKq08Dao;
import io.renren.modules.regul.kq08.dto.BKq08DTO;
import io.renren.modules.regul.kq08.entity.BKq08Entity;
import io.renren.modules.regul.kq08.service.BKq08Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 管理人员请假
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-18
 */
@Service
public class BKq08ServiceImpl extends CrudServiceImpl<BKq08Dao, BKq08Entity, BKq08DTO> implements BKq08Service {

    @Override
    public QueryWrapper<BKq08Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<BKq08Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}