package io.renren.modules.regul.ps04.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
public class Ps04Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps0401;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "企业ID")
    private BigDecimal cp0101;
    @Excel(name = "人员ID")
    private BigDecimal ps0101;
    @Excel(name = "岗位类型")
    private String jobtype;
    @Excel(name = "职员状态")
    private String managestatus;
    @Excel(name = "管理类型")
    private String managetype;
    @Excel(name = "制卡采集照片")
    private String photo;
    @Excel(name = "是否有劳动合同")
    private String hascontract;
    @Excel(name = "是否购买工伤或意外伤害保险")
    private String hasbuyinsurance;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}