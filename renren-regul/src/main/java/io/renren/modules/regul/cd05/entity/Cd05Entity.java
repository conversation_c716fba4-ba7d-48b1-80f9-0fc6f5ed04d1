package io.renren.modules.regul.cd05.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 诚信评价申诉表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_CD05")
public class Cd05Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long cd0501;
    /**
     * 不良行为ID
     */
    private Long cd0301;
    /**
     * 申诉描述
     */
    private String appeal;
    /**
     * 审核状态
     */
    private String auditstatus;
    /**
     * 审核时间
     */
    private Date auditdate;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核结果
     */
    private String auditresult;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}