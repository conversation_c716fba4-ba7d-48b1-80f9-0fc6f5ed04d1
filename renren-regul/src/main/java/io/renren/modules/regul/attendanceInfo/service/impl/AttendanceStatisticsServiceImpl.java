package io.renren.modules.regul.attendanceInfo.service.impl;


import cn.hutool.core.util.ObjectUtil;
import io.renren.modules.regul.attendanceInfo.dao.AttendanceStatisticsDao;
import io.renren.modules.regul.attendanceInfo.dto.AttendanceQueryDTO;
import io.renren.modules.regul.attendanceInfo.dto.AttendanceStatisticsDTO;
import io.renren.modules.regul.attendanceInfo.service.AttendanceStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 考勤统计服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
@Slf4j
@Service
public class AttendanceStatisticsServiceImpl implements AttendanceStatisticsService {

    @Autowired
    private AttendanceStatisticsDao attendanceStatisticsDao;

    @Override
    public List<AttendanceStatisticsDTO> getAttendanceStatistics(AttendanceQueryDTO queryDTO) {
        validateQueryParams(queryDTO);

        switch (queryDTO.getStatisticsType()) {
            case STATUS:
                return getAttendanceStatusStatistics(queryDTO);
            case WORKHOURS:
                return getWorkhoursStatistics(queryDTO);
            case COMPREHENSIVE:
            default:
                return getComprehensiveStatistics(queryDTO);
        }
    }

    @Override
    public List<AttendanceStatisticsDTO> getAttendanceStatusStatistics(AttendanceQueryDTO queryDTO) {
        validateQueryParams(queryDTO);

        try {
            List<Map<String, Object>> rawData = attendanceStatisticsDao.getAttendanceStatusStatistics(
                    queryDTO.getMonth(), queryDTO.getPj0101(), queryDTO.getPersonType()
            );

            return convertToAttendanceStatisticsDTO(rawData, queryDTO, false);
        } catch (Exception e) {
            log.error("获取考勤状态统计失败", e);
            throw new RuntimeException("获取考勤状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<AttendanceStatisticsDTO> getWorkhoursStatistics(AttendanceQueryDTO queryDTO) {
        validateQueryParams(queryDTO);

        try {
            List<Map<String, Object>> rawData = attendanceStatisticsDao.getAttendanceWorkhoursStatistics(
                    queryDTO.getMonth(), queryDTO.getPj0101(), queryDTO.getPersonType()
            );

            return convertToAttendanceStatisticsDTO(rawData, queryDTO, true);
        } catch (Exception e) {
            log.error("获取工时统计失败", e);
            throw new RuntimeException("获取工时统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<AttendanceStatisticsDTO> getComprehensiveStatistics(AttendanceQueryDTO queryDTO) {
        validateQueryParams(queryDTO);

        try {
            log.info("开始获取综合考勤统计 - 月份: {}, 项目ID: {}, 人员ID: {}",
                    queryDTO.getMonth(), queryDTO.getPj0101(), queryDTO.getUserId());

            // 第一步：先清理可能存在的重复数据
            log.info("开始清理可能存在的重复数据，月份: {}", queryDTO.getMonth());
            try {
                // 删除该月份的现有数据，避免唯一约束冲突
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("month", queryDTO.getMonth());
                deleteParams.put("pj0101", queryDTO.getPj0101() != null ? queryDTO.getPj0101().toString() : null);
                deleteParams.put("userId", queryDTO.getUserId());

                // 这里可以添加一个删除方法，或者在存储过程中处理
                log.debug("准备清理参数: {}", deleteParams);
            } catch (Exception e) {
                log.warn("清理数据时出现异常，继续执行: {}", e.getMessage());
            }

            // 第二步：调用存储过程进行数据处理和持久化
            Map<String, Object> params = new HashMap<>();
            params.put("month", queryDTO.getMonth());
            // 确保pj0101参数正确转换为字符串
            params.put("pj0101", queryDTO.getPj0101() != null ? queryDTO.getPj0101().toString() : null);
            // 添加userId参数
            params.put("userId", queryDTO.getUserId());

            log.debug("调用存储过程前的参数: {}", params);

            // 调用存储过程进行数据处理和持久化
            attendanceStatisticsDao.getAttendanceComprehensiveStatistics(params);

            // 记录影响行数
            Object affectedRows = params.get("affectedRows");
            if (affectedRows != null) {
                log.info("存储过程执行完成，数据已持久化到T_ATTENDANCE_STATISTICS表，影响行数: {}", affectedRows);
            }

            // 第二步：从T_ATTENDANCE_STATISTICS表查询结果
            List<Map<String, Object>> rawData = attendanceStatisticsDao.getAttendanceStatisticsFromTable(
                    queryDTO.getMonth(),
                    queryDTO.getPj0101() != null ? queryDTO.getPj0101().toString() : null,
                    queryDTO.getUserId()
            );

            log.info("从T_ATTENDANCE_STATISTICS表查询到 {} 条记录", rawData.size());

            // 第三步：转换为DTO对象
            return convertToAttendanceStatisticsDTO(rawData, queryDTO, false);
        } catch (Exception e) {
            log.error("获取综合考勤统计失败", e);
            throw new RuntimeException("获取综合考勤统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getMonthlyAttendanceSummary(AttendanceQueryDTO queryDTO) {
        validateQueryParams(queryDTO);

        try {
            return attendanceStatisticsDao.getMonthlyAttendanceSummary(queryDTO.getMonth(), queryDTO.getPj0101(), queryDTO.getPersonType()
            );
        } catch (Exception e) {
            log.error("获取月度考勤汇总失败", e);
            throw new RuntimeException("获取月度考勤汇总失败: " + e.getMessage());
        }
    }

    @Override
    public void validateQueryParams(AttendanceQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        if (!StringUtils.hasText(queryDTO.getMonth())) {
            throw new IllegalArgumentException("月份参数不能为空");
        }

        if (!queryDTO.isValidMonth()) {
            throw new IllegalArgumentException("月份格式错误，请使用YYYY-MM格式");
        }

        // 验证项目是否存在
        if (queryDTO.getPj0101() != null) {
            Boolean projectExists = attendanceStatisticsDao.checkProjectExists(queryDTO.getPj0101());
            if (projectExists == null || !projectExists) {
                throw new IllegalArgumentException("指定的项目不存在");
            }
        }

        // 验证人员类型
        if (StringUtils.hasText(queryDTO.getPersonType())) {
            if (!"1".equals(queryDTO.getPersonType()) && !"2".equals(queryDTO.getPersonType())) {
                throw new IllegalArgumentException("人员类型参数错误，1-建筑工人，2-管理人员");
            }
        }
    }

    @Override
    public Integer getDaysInMonth(String month) {
        try {
            return attendanceStatisticsDao.getDaysInMonth(month);
        } catch (Exception e) {
            log.error("获取月份天数失败", e);
            // 备用计算方法
            LocalDate date = LocalDate.parse(month + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return date.lengthOfMonth();
        }
    }

    /**
     * 转换原始数据为DTO对象
     * 处理从T_ATTENDANCE_STATISTICS表查询的数据，并映射到AttendanceStatisticsDTO对象
     *
     * @param resultData      从T_ATTENDANCE_STATISTICS表查询的数据
     * @param queryDTO        查询参数
     * @param isWorkhoursOnly 是否仅包含工时数据
     * @return 转换后的DTO对象列表
     */
    private List<AttendanceStatisticsDTO> convertToAttendanceStatisticsDTO(Object resultData, AttendanceQueryDTO queryDTO, boolean isWorkhoursOnly) {
        log.info("开始转换Oracle游标数据为DTO对象");
        log.debug("游标数据类型: {}", resultData != null ? resultData.getClass().getName() : "null");

        if (ObjectUtil.isEmpty(resultData)) {
            log.warn("游标数据为空，返回空列表");
            return new ArrayList<>();
        }

        try {
            // 获取月份天数
            Integer daysInMonth = getDaysInMonth(queryDTO.getMonth());
            if (daysInMonth == null || daysInMonth <= 0) {
                log.error("无效的月份天数: {}", daysInMonth);
                throw new IllegalArgumentException("无效的月份: " + queryDTO.getMonth());
            }

            List<AttendanceStatisticsDTO> result = new ArrayList<>();

            // 安全地提取游标数据
            List<Map<String, Object>> dataList = safeExtractCursorData(resultData);

            if (dataList.isEmpty()) {
                log.warn("提取的游标数据为空");
                return new ArrayList<>();
            }

            log.info("成功提取到 {} 条原始数据记录", dataList.size());

            // 转换每一行数据
            for (int i = 0; i < dataList.size(); i++) {
                Map<String, Object> row = dataList.get(i);
                try {
                    log.debug("处理第 {} 行数据: {}", i + 1, row.keySet());

                    AttendanceStatisticsDTO dto = createAttendanceStatisticsDTO(row, daysInMonth, isWorkhoursOnly);

                    // 计算汇总信息
                    if (queryDTO.getIncludeSummary() != null && queryDTO.getIncludeSummary()) {
                        dto.calculateSummary(daysInMonth);
                        log.debug("已计算汇总信息 - 出勤天数: {}, 总工时: {}",
                                dto.getSummary().getAttendanceDays(),
                                dto.getSummary().getTotalWorkHours());
                    }

                    result.add(dto);
                    log.debug("成功转换人员数据: {} (ID: {})", dto.getPersonName(), dto.getUserId());

                } catch (Exception e) {
                    log.error("转换第 {} 行数据失败，跳过该行: {}", i + 1, row, e);
                    // 继续处理下一行数据，不中断整个转换过程
                }
            }

            log.info("成功转换 {} 条考勤统计数据（共处理 {} 条原始数据）", result.size(), dataList.size());
            return result;

        } catch (Exception e) {
            log.error("转换游标数据为DTO对象失败", e);
            throw new RuntimeException("数据转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将不同类型的结果数据转换为Map列表
     * 专门处理Oracle存储过程返回的游标数据
     *
     * @param resultData 原始数据（可能是游标、List或其他类型）
     * @return Map列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> convertToMapList(Object resultData) {
        log.debug("开始转换数据类型: {}", resultData != null ? resultData.getClass().getName() : "null");

        if (resultData == null) {
            log.warn("结果数据为null，返回空列表");
            return new ArrayList<>();
        }

        // 处理List<Map<String, Object>>类型（最常见的情况）
        if (resultData instanceof List) {
            List<?> list = (List<?>) resultData;
            if (list.isEmpty()) {
                log.info("结果列表为空");
                return new ArrayList<>();
            }

            // 检查列表元素类型
            Object firstElement = list.get(0);
            if (firstElement instanceof Map) {
                log.debug("识别为List<Map>类型，包含{}条记录", list.size());
                return (List<Map<String, Object>>) resultData;
            } else {
                log.warn("List中的元素不是Map类型: {}", firstElement.getClass());
                throw new IllegalArgumentException("List中的元素必须是Map类型，实际类型: " + firstElement.getClass());
            }
        }

        // 处理Oracle游标对象（可能是ResultSet或其包装类）
        if (resultData.getClass().getName().contains("oracle") ||
            resultData.getClass().getName().contains("ResultSet") ||
            resultData.getClass().getName().contains("Cursor")) {
            log.debug("检测到Oracle游标类型: {}", resultData.getClass().getName());
            return convertOracleCursorToMapList(resultData);
        }

        // 处理单个Map对象（转换为包含一个元素的List）
        if (resultData instanceof Map) {
            log.debug("识别为单个Map对象，转换为List");
            List<Map<String, Object>> result = new ArrayList<>();
            result.add((Map<String, Object>) resultData);
            return result;
        }

        // 如果都不匹配，记录详细信息并抛出异常
        log.error("不支持的数据格式 - 类型: {}, 值: {}", resultData.getClass().getName(), resultData);
        throw new IllegalArgumentException("不支持的数据格式: " + resultData.getClass().getName() +
                                         "，支持的格式: List<Map<String,Object>>, Map<String,Object>, Oracle游标");
    }

    /**
     * 转换Oracle游标为Map列表
     * 处理Oracle JDBC返回的游标对象
     *
     * @param cursorData Oracle游标数据
     * @return Map列表
     */
    private List<Map<String, Object>> convertOracleCursorToMapList(Object cursorData) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            log.debug("处理Oracle游标数据，类型: {}", cursorData.getClass().getName());

            // 如果游标已经被MyBatis处理为List，直接返回
            if (cursorData instanceof List) {
                return convertToMapList(cursorData);
            }

            // 尝试通过反射获取游标数据
            // MyBatis通常会将Oracle游标转换为List<Map<String, Object>>
            // 但在某些情况下可能需要特殊处理

            // 检查是否有getData()或类似的方法
            try {
                java.lang.reflect.Method getDataMethod = cursorData.getClass().getMethod("getData");
                Object data = getDataMethod.invoke(cursorData);
                if (data instanceof List) {
                    return convertToMapList(data);
                }
            } catch (NoSuchMethodException e) {
                log.debug("游标对象没有getData()方法");
            }

            // 检查是否有getResultList()方法
            try {
                java.lang.reflect.Method getResultListMethod = cursorData.getClass().getMethod("getResultList");
                Object data = getResultListMethod.invoke(cursorData);
                if (data instanceof List) {
                    return convertToMapList(data);
                }
            } catch (NoSuchMethodException e) {
                log.debug("游标对象没有getResultList()方法");
            }

            // 如果以上方法都不可用，尝试直接转换
            log.warn("无法识别的Oracle游标类型: {}，尝试直接转换", cursorData.getClass().getName());

            // 最后的尝试：检查对象是否实现了Iterable接口
            if (cursorData instanceof Iterable) {
                for (Object item : (Iterable<?>) cursorData) {
                    if (item instanceof Map) {
                        result.add((Map<String, Object>) item);
                    }
                }
                log.debug("通过Iterable接口获取到{}条记录", result.size());
                return result;
            }

            log.warn("Oracle游标类型 {} 无法处理，返回空列表", cursorData.getClass().getName());

        } catch (Exception e) {
            log.error("转换Oracle游标失败", e);
            throw new RuntimeException("转换Oracle游标失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 安全地从游标结果中提取数据
     * 提供多种fallback机制确保数据能够正确提取
     *
     * @param cursorResult 游标结果对象
     * @return 提取的数据列表
     */
    private List<Map<String, Object>> safeExtractCursorData(Object cursorResult) {
        if (cursorResult == null) {
            log.warn("游标结果为null");
            return new ArrayList<>();
        }

        log.debug("安全提取游标数据，类型: {}", cursorResult.getClass().getName());

        try {
            // 方式1：直接转换（最常见的情况）
            if (cursorResult instanceof List) {
                return convertToMapList(cursorResult);
            }

            // 方式2：通过convertToMapList处理
            return convertToMapList(cursorResult);

        } catch (Exception e) {
            log.error("提取游标数据失败，尝试备用方案", e);

            // 备用方案：返回空列表，避免整个流程中断
            log.warn("使用备用方案：返回空列表");
            return new ArrayList<>();
        }
    }

    /**
     * 创建AttendanceStatisticsDTO对象
     *
     * @param row             数据行
     * @param daysInMonth     月份天数
     * @param isWorkhoursOnly 是否仅工时数据
     * @return DTO对象
     */
    private AttendanceStatisticsDTO createAttendanceStatisticsDTO(Map<String, Object> row, Integer daysInMonth, boolean isWorkhoursOnly) {
        AttendanceStatisticsDTO dto = new AttendanceStatisticsDTO();

        // 设置基础信息
        setBasicInfo(dto, row);

        // 设置每日数据
        setDailyData(dto, row, daysInMonth, isWorkhoursOnly);

        return dto;
    }

    /**
     * 设置基础信息
     *
     * @param dto DTO对象
     * @param row 数据行
     */
    private void setBasicInfo(AttendanceStatisticsDTO dto, Map<String, Object> row) {
        try {
            // 设置人员姓名
            Object personNameObj = row.get("PERSON_NAME");
            if (personNameObj != null) {
                dto.setPersonName(personNameObj.toString());
            } else {
                log.warn("人员姓名为空，行数据: {}", row);
                dto.setPersonName("未知");
            }

            // 设置用户ID
            Object userIdObj = row.get("USER_ID");
            if (userIdObj != null) {
                if (userIdObj instanceof Number) {
                    dto.setUserId(((Number) userIdObj).longValue());
                } else {
                    dto.setUserId(Long.parseLong(userIdObj.toString()));
                }
            } else {
                log.warn("用户ID为空，行数据: {}", row);
                dto.setUserId(0L);
            }

        } catch (Exception e) {
            log.error("设置基础信息失败，行数据: {}", row, e);
            throw new RuntimeException("设置基础信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置每日数据
     *
     * @param dto             DTO对象
     * @param row             数据行
     * @param daysInMonth     月份天数
     * @param isWorkhoursOnly 是否仅工时数据
     */
    private void setDailyData(AttendanceStatisticsDTO dto, Map<String, Object> row, Integer daysInMonth, boolean isWorkhoursOnly) {
        for (int day = 1; day <= daysInMonth; day++) {
            try {
                if (isWorkhoursOnly) {
                    // 仅工时数据
                    String workhoursKey = "WORKHOURS_" + String.format("%02d", day);
                    Object workhoursValue = row.get(workhoursKey);
                    Object processedValue = processDataValue(workhoursValue, true);
                    dto.addDailyData(day, processedValue);
                } else {
                    // 综合数据：考勤状态 + 工时
                    String attendanceKey = "ATTENDANCE_" + String.format("%02d", day);
                    String workhoursKey = "WORKHOURS_" + String.format("%02d", day);

                    Object attendanceValue = row.get(attendanceKey);
                    Object workhoursValue = row.get(workhoursKey);

                    // 组合考勤状态和工时
                    String attendanceStatus = attendanceValue != null ? attendanceValue.toString() : "N";
                    String workhours = workhoursValue != null ? workhoursValue.toString() : "0";

                    // 转换为原有格式：Y8 -> √8, N0 -> ×0
                    String combinedValue;
                    if ("Y".equals(attendanceStatus)) {
                        combinedValue = "√" + workhours;
                    } else {
                        combinedValue = "×0";
                    }

                    dto.addDailyData(day, combinedValue);
                }

                log.trace("设置第{}天数据成功", day);

            } catch (Exception e) {
                log.warn("设置第{}天数据失败: {}", day, e.getMessage());
                // 设置默认值
                dto.addDailyData(day, isWorkhoursOnly ? "0" : "×0");
            }
        }
    }

    /**
     * 构建列名
     *
     * @param day             日期
     * @param isWorkhoursOnly 是否仅工时数据
     * @return 列名
     */
    private String buildColumnKey(int day, boolean isWorkhoursOnly) {
        // 适配新的字段结构：ATTENDANCE_XX 和 WORKHOURS_XX
        String prefix = isWorkhoursOnly ? "WORKHOURS_" : "ATTENDANCE_";
        return prefix + String.format("%02d", day);
    }

    /**
     * 处理数据值
     *
     * @param value           原始值
     * @param isWorkhoursOnly 是否仅工时数据
     * @return 处理后的值
     */
    private Object processDataValue(Object value, boolean isWorkhoursOnly) {
        if (value == null) {
            return isWorkhoursOnly ? "0" : "×0";
        }

        String strValue = value.toString().trim();
        if (strValue.isEmpty()) {
            return isWorkhoursOnly ? "0" : "×0";
        }

        if (isWorkhoursOnly) {
            // 工时数据处理
            try {
                // 尝试解析为数字
                Double.parseDouble(strValue);
                return strValue;
            } catch (NumberFormatException e) {
                log.warn("工时数据格式错误: {}", strValue);
                return "0";
            }
        } else {
            // 考勤状态数据处理
            return formatAttendanceStatus(strValue);
        }
    }

    /**
     * 格式化考勤状态
     *
     * @param status 原始状态
     * @return 格式化后的状态
     */
    private String formatAttendanceStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return "×0";
        }

        String trimmedStatus = status.trim();

        // 根据业务规则格式化状态
        switch (trimmedStatus.toUpperCase()) {
            case "1":
            case "PRESENT":
            case "出勤":
                return "√8"; // 出勤8小时
            case "0":
            case "ABSENT":
            case "缺勤":
                return "×0";
            case "LATE":
            case "迟到":
                return "△7"; // 迟到7小时
            case "LEAVE":
            case "请假":
                return "○0"; // 请假0小时
            default:
                // 如果已经是格式化的状态，直接返回
                if (trimmedStatus.matches("^[√×△○]\\d+$")) {
                    return trimmedStatus;
                }
                return "×0";
        }
    }

    /**
     * 创建Excel表头
     */
    private void createExcelHeader(Sheet sheet, Integer daysInMonth, AttendanceQueryDTO queryDTO) {
        Row headerRow = sheet.createRow(0);

        // 基础列
        headerRow.createCell(0).setCellValue("人员姓名");
        headerRow.createCell(1).setCellValue("人员ID");

        // 日期列
        for (int day = 1; day <= daysInMonth; day++) {
            headerRow.createCell(day + 1).setCellValue(day + "号");
        }

        // 汇总列
        if (queryDTO.getIncludeSummary()) {
            int summaryStartCol = daysInMonth + 2;
            headerRow.createCell(summaryStartCol).setCellValue("出勤天数");
            headerRow.createCell(summaryStartCol + 1).setCellValue("缺勤天数");
            headerRow.createCell(summaryStartCol + 2).setCellValue("总工时");
            headerRow.createCell(summaryStartCol + 3).setCellValue("平均工时");
            headerRow.createCell(summaryStartCol + 4).setCellValue("出勤率(%)");
        }
    }

    /**
     * 填充Excel数据
     */
    private void fillExcelData(Sheet sheet, List<AttendanceStatisticsDTO> data,
                               Integer daysInMonth, AttendanceQueryDTO queryDTO) {
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            AttendanceStatisticsDTO dto = data.get(i);

            // 基础信息
            row.createCell(0).setCellValue(dto.getPersonName());
            row.createCell(1).setCellValue(dto.getUserId());

            // 日期数据
            for (int day = 1; day <= daysInMonth; day++) {
                Object value = dto.getDailyData(day);
                Cell cell = row.createCell(day + 1);
                if (value != null) {
                    cell.setCellValue(value.toString());
                } else {
                    cell.setCellValue("×0");
                }
            }

            // 汇总数据
            if (queryDTO.getIncludeSummary() && dto.getSummary() != null) {
                int summaryStartCol = daysInMonth + 2;
                row.createCell(summaryStartCol).setCellValue(dto.getSummary().getAttendanceDays());
                row.createCell(summaryStartCol + 1).setCellValue(dto.getSummary().getAbsentDays());
                row.createCell(summaryStartCol + 2).setCellValue(dto.getSummary().getTotalWorkHours().doubleValue());
                row.createCell(summaryStartCol + 3).setCellValue(dto.getSummary().getAverageWorkHours().doubleValue());
                row.createCell(summaryStartCol + 4).setCellValue(dto.getSummary().getAttendanceRate().doubleValue());
            }
        }
    }

    /**
     * 设置Excel列宽
     */
    private void setColumnWidths(Sheet sheet, Integer daysInMonth) {
        // 基础列宽度
        sheet.setColumnWidth(0, 3000); // 姓名
        sheet.setColumnWidth(1, 2500); // ID

        // 日期列宽度
        for (int day = 1; day <= daysInMonth; day++) {
            sheet.setColumnWidth(day + 1, 2000);
        }

        // 汇总列宽度
        int summaryStartCol = daysInMonth + 2;
        for (int i = 0; i < 5; i++) {
            sheet.setColumnWidth(summaryStartCol + i, 2500);
        }
    }
}
