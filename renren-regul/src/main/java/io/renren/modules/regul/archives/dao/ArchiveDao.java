package io.renren.modules.regul.archives.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.regul.archives.dto.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface ArchiveDao extends BaseDao<ArchiveInfo> {
    /**
     * 查询人员列表
     * @param params Map<String, Object>
     * @return List<PersonPage>
     */
    List<PersonPage> selectPersonPageList(Map<String, Object> params);

    /**
     * 查询参建单位列表
     * @param params Map<String, Object>
     * @return List<PartUnitPage>
     */
    List<PartUnitPage> selectPartUnitPageList(Map<String, Object> params);

    /**
     * 获取班组列表
     * @param params Map<String, Object>
     * @return List<TeamPage>
     */
    List<TeamPage> selectTeamPageList(Map<String, Object> params);

    /**
     * 获取附件列表
     * @param params Map<String, Object>
     * @return List<FilePage>
     */
    List<FilePage> selectFilePageList(Map<String, Object> params);

    /**
     * 获取项目列表
     * @param params Map<String, Object>
     * @return List<ProjectPage>
     */
    List<ProjectPage> selectProjectPageList(Map<String, Object> params);
}
