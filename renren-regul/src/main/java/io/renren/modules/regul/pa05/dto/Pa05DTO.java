package io.renren.modules.regul.pa05.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工人工资单
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "工人工资单")
public class Pa05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pa0501;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty(value = "工资发放月份")
    private Date issuedate;
    @ApiModelProperty(value = "工人数量")
    private Integer workercount;
    @ApiModelProperty(value = "工资发放总金额")
    private BigDecimal accountnum;
    @ApiModelProperty(value = "工资单状态")
    private String salarystatus;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "班组名称")
    private String teamname;
}