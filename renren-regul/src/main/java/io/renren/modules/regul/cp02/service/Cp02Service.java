package io.renren.modules.regul.cp02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.regul.cp02.dto.Cp02AuditDTO;
import io.renren.modules.regul.cp02.dto.Cp02DTO;
import io.renren.modules.regul.cp02.dto.Cp02PageDTO;
import io.renren.modules.regul.cp02.entity.Cp02Entity;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
public interface Cp02Service extends CrudService<Cp02Entity, Cp02DTO> {
    /**
     * 功能描述: <br> 参建单位列表分页查询 <br/>
     * 创建时间:  2020-06-29 8:56
     *
     * @param params params
     * @return PageData<Cp02PageDTO>
     * <AUTHOR>
     */
    PageData<Cp02PageDTO> pageList(Map<String, Object> params);

    /**
     * 审核列表
     * @param params
     * @return
     */
    PageData<Cp02AuditDTO> auditPageList(Map<String, Object> params);

    /**
     * 审核
     * @param dto
     */
    Result audit(Cp02AuditDTO dto);

    Cp02DTO getInfo(Map<String, Object> params);
}