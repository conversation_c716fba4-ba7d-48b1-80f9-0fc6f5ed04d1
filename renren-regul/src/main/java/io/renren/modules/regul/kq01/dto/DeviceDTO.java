package io.renren.modules.regul.kq01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-07-19 10:16
 */
@Data
@ApiModel(value = "待复制考勤设备信息列表")
public class DeviceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "设备序列号")
    private String deviceKey;


}
