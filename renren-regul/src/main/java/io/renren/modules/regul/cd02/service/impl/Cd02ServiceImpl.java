package io.renren.modules.regul.cd02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.ot01.dao.Ot01Dao;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.regul.cd01.dao.Cd01Dao;
import io.renren.modules.regul.cd01.entity.Cd01Entity;
import io.renren.modules.regul.cd02.dao.Cd02Dao;
import io.renren.modules.regul.cd02.dto.Cd02DTO;
import io.renren.modules.regul.cd02.entity.Cd02Entity;
import io.renren.modules.regul.cd02.service.Cd02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 良好行为记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Service
public class Cd02ServiceImpl extends CrudServiceImpl<Cd02Dao, Cd02Entity, Cd02DTO> implements Cd02Service {
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Cd01Dao cd01Dao;

    @Override
    public QueryWrapper<Cd02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Cd02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<Cd02DTO> pageList(Map<String, Object> params) {
        IPage<Cd02Entity> page = getPage(params, "", false);
        List<Cd02DTO> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), Cd02DTO.class);
    }

    @Override
    public PageData<Cd02DTO> publicizepage(Map<String, Object> params) {
        IPage<Cd02Entity> page = getPage(params, "", false);
        List<Cd02DTO> list = baseDao.publicizepage(params);
        return getPageData(list, page.getTotal(), Cd02DTO.class);
    }

    @Override
    public Result getInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        Long cd0201 = Long.valueOf(params.get("cd0201").toString());
        Cd02DTO dto = baseDao.selectInfoById(cd0201);
        List<Ot01DTO> files = ot01Dao.getBusinessData(cd0201, "58");
        dto.setFiles(files);
        return result.ok(dto);
    }

    @Override
    public Result sure(Cd02DTO dto) {
        Result<Object> result = new Result<>();
        Cd02Entity cd02 = baseDao.selectById(dto.getCd0201());
        String areacode = SecurityUser.getDeptAreaCode();
        String userlevel;
        String lastcode = areacode.substring(4, 6);
        if ("00".equals(lastcode)) {
            userlevel = "2";
        } else {
            userlevel = "1";
        }
        if (!cd02.getRewardstatus().equals(userlevel)) {
            return result.error("此用户无操作权限！");
        }
        cd02.setScore(dto.getScore());
        cd02.setUsefuldate(dto.getUsefuldate());
        cd02.setPublicizedate(dto.getPublicizedate());
        cd02.setRewardstatus(String.valueOf(Long.valueOf(cd02.getRewardstatus()) + 1));
        baseDao.updateById(cd02);
        Cd01Entity cd01 = cd01Dao.selectById(cd02.getCd0101());
        cd01.setCreditscore(cd01.getCreditscore() + dto.getScore());
        cd01Dao.updateById(cd01);
        return result;
    }
}