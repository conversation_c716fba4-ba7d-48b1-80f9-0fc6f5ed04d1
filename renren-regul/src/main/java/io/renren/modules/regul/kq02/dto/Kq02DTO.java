package io.renren.modules.regul.kq02.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人考勤信息")
public class Kq02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long kq0201;

    @ApiModelProperty(value = "人员ID")
    private Long userId;

    @ApiModelProperty(value = "人员类型")
    private String personType;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "设备序列号")
    private String deviceserialno;

    @ApiModelProperty(value = "考勤时间")
    private Date checkdate;

    @ApiModelProperty(value = "进出方向")
    private String direction;

    @ApiModelProperty(value = "通行方式")
    private String attendtype;

    @ApiModelProperty(value = "经度")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "刷卡近照")
    private String imageUrl;


}