package io.renren.modules.regul.attendanceInfo.service;


import io.renren.modules.regul.attendanceInfo.dto.AttendanceQueryDTO;
import io.renren.modules.regul.attendanceInfo.dto.AttendanceStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * 考勤统计服务接口
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
public interface AttendanceStatisticsService {

    /**
     * 获取考勤统计数据（根据查询类型自动选择）
     *
     * @param queryDTO 查询参数
     * @return 考勤统计结果列表
     */
    List<AttendanceStatisticsDTO> getAttendanceStatistics(AttendanceQueryDTO queryDTO);

    /**
     * 获取考勤状态统计（仅状态，不含工时）
     *
     * @param queryDTO 查询参数
     * @return 考勤状态统计结果
     */
    List<AttendanceStatisticsDTO> getAttendanceStatusStatistics(AttendanceQueryDTO queryDTO);

    /**
     * 获取工时统计（仅工时，不含状态）
     *
     * @param queryDTO 查询参数
     * @return 工时统计结果
     */
    List<AttendanceStatisticsDTO> getWorkhoursStatistics(AttendanceQueryDTO queryDTO);

    /**
     * 获取综合考勤统计（状态+工时）
     *
     * @param queryDTO 查询参数
     * @return 综合统计结果
     */
    List<AttendanceStatisticsDTO> getComprehensiveStatistics(AttendanceQueryDTO queryDTO);

    /**
     * 获取月度考勤汇总信息
     *
     * @param queryDTO 查询参数
     * @return 汇总信息
     */
    Map<String, Object> getMonthlyAttendanceSummary(AttendanceQueryDTO queryDTO);

    /**
     * 验证查询参数
     *
     * @param queryDTO 查询参数
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    void validateQueryParams(AttendanceQueryDTO queryDTO);

    /**
     * 获取指定月份的天数
     *
     * @param month 月份（YYYY-MM格式）
     * @return 该月天数
     */
    Integer getDaysInMonth(String month);
}
