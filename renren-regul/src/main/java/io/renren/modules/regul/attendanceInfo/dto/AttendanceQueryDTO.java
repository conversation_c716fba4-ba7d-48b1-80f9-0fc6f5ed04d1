package io.renren.modules.regul.attendanceInfo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 考勤查询参数DTO
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
@Data
@ApiModel(value = "考勤查询参数")
public class AttendanceQueryDTO implements Serializable {
    private static final long serialVersionUID = 8779988329819860657L;
    /**
     * 月份参数（必填）
     * 格式：YYYY-MM，例如：2024-03
     */
    @NotBlank(message = "月份参数不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "月份格式错误，请使用YYYY-MM格式")
    @ApiModelProperty(value = "月份参数（必填）", required = true)
    private String month;

    /**
     * 项目ID（可选）
     */
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    /**
     * 人员类型（可选）
     * 1-建筑工人，2-管理人员
     */
    @ApiModelProperty(value = "人员类型")
    private String personType;

    /**
     * 人员姓名（模糊查询，可选）
     */
    @ApiModelProperty(value = "人员姓名")
    private String personName;

    /**
     * 人员ID（可选）
     */
    @ApiModelProperty(value = "人员ID")
    private String userId;

    /**
     * 统计类型
     * STATUS-仅考勤状态，WORKHOURS-仅工时，COMPREHENSIVE-综合统计
     */
    private StatisticsType statisticsType = StatisticsType.COMPREHENSIVE;

    /**
     * 是否包含汇总信息
     */
    @ApiModelProperty(value = "是否包含汇总信息")
    private Boolean includeSummary = true;

    /**
     * 统计类型枚举
     */
    public enum StatisticsType {
        /**
         * 仅考勤状态（√/×）
         */
        STATUS,
        
        /**
         * 仅工时统计
         */
        WORKHOURS,
        
        /**
         * 综合统计（状态+工时）
         */
        COMPREHENSIVE
    }

    /**
     * 验证月份格式
     *
     * @return 是否有效
     */
    public boolean isValidMonth() {
        if (month == null || month.length() != 7) {
            return false;
        }
        
        try {
            String[] parts = month.split("-");
            if (parts.length != 2) {
                return false;
            }
            
            int year = Integer.parseInt(parts[0]);
            int monthNum = Integer.parseInt(parts[1]);
            
            return year >= 2000 && year <= 2100 && monthNum >= 1 && monthNum <= 12;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取年份
     *
     * @return 年份
     */
    public Integer getYear() {
        if (!isValidMonth()) {
            return null;
        }
        return Integer.parseInt(month.substring(0, 4));
    }

    /**
     * 获取月份数字
     *
     * @return 月份（1-12）
     */
    public Integer getMonthNumber() {
        if (!isValidMonth()) {
            return null;
        }
        return Integer.parseInt(month.substring(5, 7));
    }
}
