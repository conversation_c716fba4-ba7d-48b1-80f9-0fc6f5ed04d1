package io.renren.modules.regul.pj11.controller;


import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.regul.pj01.dto.Pj01DTO;
import io.renren.modules.regul.pj01.service.Pj01Service;
import io.renren.modules.regul.pj11.dto.Pj11DTO;
import io.renren.modules.regul.pj11.dto.Pj11Page;
import io.renren.modules.regul.pj11.service.Pj11Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 项目抽查记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-06-04
 */
@RestController
@RequestMapping("regul/pj11")
@Api(tags = "项目抽查记录表")
public class Pj11Controller {
    @Resource
    private Pj11Service pj11Service;
    @Resource
    private Pj01Service pj01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "检查时间", value = "pj1102", paramType = "query", dataType = "String", example = "2025-06-03"),
            @ApiImplicitParam(name = "标题", value = "title", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("regul:pj11:page")
    public Result<PageData<Pj11Page>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<Pj11Page> page = pj11Service.pageList(params);

        return new Result<PageData<Pj11Page>>().ok(page);
    }

    @GetMapping("getProjectList")
    @ApiOperation("查询项目信息")
    public Result<List<Pj01DTO>> getProjectList(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<Pj01DTO> data = pj01Service.list(params);

        return new Result<List<Pj01DTO>>().ok(data);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("regul:pj11:info")
    public Result<Pj11DTO> getInfo(@PathVariable("id") Long id) {

        Pj11DTO data = pj11Service.getInfo(id);

        return new Result<Pj11DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("regul:pj11:save")
    public Result<Object> save(@RequestBody Pj11DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj11Service.saveInfo(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("regul:pj11:update")
    public Result<Object> update(@RequestBody Pj11DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj11Service.updateInfo(dto);

        return new Result<>();
    }
}