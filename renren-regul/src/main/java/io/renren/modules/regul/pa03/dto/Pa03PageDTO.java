package io.renren.modules.regul.pa03.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工资代发明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-21
 */
@Data
@ApiModel(value = "工资代发明细表")
public class Pa03PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long pa0301;
    @ApiModelProperty(value = "专户流水ID")
    private Long pa0201;
    @ApiModelProperty(value = "人员名称")
    private String name;
    @ApiModelProperty(value = "金额")
    private Long accountnum;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "交易日期")
    private Date accountdate;
    @ApiModelProperty(value = "对方账号")
    @Desensitized(type = DesenTypeEum.BANK_CARD)
    private String partaccount;
    @ApiModelProperty(value = "对方户名")
    private String partname;
    @ApiModelProperty(value = "备注")
    private String description;
}