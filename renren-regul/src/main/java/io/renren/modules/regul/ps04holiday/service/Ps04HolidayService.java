package io.renren.modules.regul.ps04holiday.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.regul.ps04holiday.dto.Ps04HolidayDTO;
import io.renren.modules.regul.ps04holiday.entity.Ps04HolidayEntity;

import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
public interface Ps04HolidayService extends CrudService<Ps04HolidayEntity, Ps04HolidayDTO> {

    PageData<Ps04HolidayDTO> pageList(Map<String, Object> params);

    Result getInfo(Map<String, Object> params);

    Result audit(Ps04HolidayDTO dto);
}