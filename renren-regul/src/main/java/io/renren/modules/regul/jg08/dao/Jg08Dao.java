package io.renren.modules.regul.jg08.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.regul.jg08.dto.Jg08DTO;
import io.renren.modules.regul.jg08.entity.Jg08Entity;
import io.renren.modules.regul.jg09.dto.Jg09DTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 工资投诉表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-16
 */
@Mapper
public interface Jg08Dao extends BaseDao<Jg08Entity> {

    List<Jg08DTO> getList(Map<String, Object> params);

    Jg08DTO selectInfoById(Long jg0801);

    List<Jg09DTO> getJg09PageList(Map<String, Object> params);

}