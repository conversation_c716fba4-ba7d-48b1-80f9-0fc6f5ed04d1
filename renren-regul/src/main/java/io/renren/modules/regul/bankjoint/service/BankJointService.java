package io.renren.modules.regul.bankjoint.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.regul.bankjoint.dto.BankJointDTO;
import io.renren.modules.regul.bankjoint.entity.BankJointEntity;
import io.renren.modules.regul.cd01.dto.Cd01DTO;
import io.renren.modules.regul.cd01.entity.Cd01Entity;
import io.renren.modules.regul.pj01.dto.Pj01DTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 诚信评价主体表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
public interface BankJointService extends CrudService<BankJointEntity, BankJointDTO> {

    List<BankJointDTO> pageList(Map<String, Object> params);

    PageData<Pj01DTO> pj01Page(Map<String, Object> params);

    void exportPage(Map<String, Object> params, HttpServletResponse response) throws IOException;

    void exportPj01Page(Map<String, Object> params, HttpServletResponse response) throws IOException;
}