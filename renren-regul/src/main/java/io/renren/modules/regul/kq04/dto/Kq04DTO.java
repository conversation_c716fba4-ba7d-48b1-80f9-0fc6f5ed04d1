package io.renren.modules.regul.kq04.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤设备指令表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-20
 */
@Data
@ApiModel(value = "考勤设备指令表")
public class Kq04DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long kq0401;

    @ApiModelProperty(value = "设备序列号")
    private String deviceserialno;

    @ApiModelProperty(value = "设备类型")
    private String terminaltype;

    @ApiModelProperty(value = "指令")
    private String cmdCode;

    @ApiModelProperty(value = "参数")
    private String params;

    @ApiModelProperty(value = "状态(0待处理，1成功，2失败)")
    private String status;

    @ApiModelProperty(value = "接口返回的信息")
    private String message;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

}