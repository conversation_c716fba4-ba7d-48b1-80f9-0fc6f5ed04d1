package io.renren.modules.regul.pj10.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.pj01.entity.Pj01Entity;
import io.renren.modules.regul.pj10.dao.Pj10Dao;
import io.renren.modules.regul.pj10.dto.Pj10DTO;
import io.renren.modules.regul.pj10.dto.ProjectDTO;
import io.renren.modules.regul.pj10.entity.Pj10Entity;
import io.renren.modules.regul.pj10.service.Pj10Service;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工资专户
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Pj10ServiceImpl extends CrudServiceImpl<Pj10Dao, Pj10Entity, Pj10DTO> implements Pj10Service {

    @Autowired
    private Pj01Dao pj01Dao;
    @Value("${expFile.patrolExport}")
    private String PATROLEXPORT;

    @Override
    public QueryWrapper<Pj10Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj10Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<ProjectDTO> pageList(Map<String, Object> params) {
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId", deptId);
        IPage<Pj10Entity> page = getPage(params, "", false);
        List<ProjectDTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), ProjectDTO.class);
    }

    @Override
    public Result patrol(Map<String, Object> params) {
        Map<String, Object> paramMap = new HashMap<>(1);
//        paramMap.put("Cpatrolhz", MapUtil.getStr(params, "patrolhz"));
        paramMap.put("Cpatrolhz", "1");
        paramMap.put("Cpatroltime", MapUtil.getStr(params, "patroltime"));
        paramMap.put("CdeptId", SecurityUser.getDeptId());
        Long pj0101 = MapUtil.getLong(params, "pj0101");
        if (pj0101 != null) {
            Pj01Entity pj01 = pj01Dao.selectById(pj0101);
            Long deptId = pj01.getDeptId();
            paramMap.put("CdeptId", deptId);
        }
        baseDao.pcPatrol(paramMap);
        return new Result();
    }

    @Override
    public Pj10DTO getInfo(Map<String, Object> params) {
        return baseDao.selectInfo(params);
    }

    @Override
    public void export(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(PATROLEXPORT);
        if (!file.exists()) {
            throw new RenException("配置的模板文件不存在");
        }
        Pj10DTO exportData = baseDao.selectInfo(params);

        Map map = JSON.parseObject(exportData.getPatrolcontent());
        map.put("name", exportData.getName());
        map.put("patrotime", DateUtil.format(DateUtil.parse(exportData.getPatroltime()), "yyyy年MM月dd日"));
        try {
            XWPFDocument doc = WordExportUtil.exportWord07(PATROLEXPORT, map);
            String fileName = exportData.getName() + "巡检报告.docx";
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            OutputStream out = response.getOutputStream();
            doc.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            throw new RenException(e.getMessage());
        }
    }
}