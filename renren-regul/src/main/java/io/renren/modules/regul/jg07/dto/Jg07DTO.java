package io.renren.modules.regul.jg07.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 投诉表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Data
@ApiModel(value = "投诉表")
public class Jg07DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long jg0701;
    @ApiModelProperty(value = "主体ID")
    private Long complaintno;
    @ApiModelProperty(value = "投诉主体1-企业2-工人")
    private String complaintsubject;
    @ApiModelProperty(value = "投诉类型")
    private String complainttype;
    @ApiModelProperty(value = "主体名称")
    private String name;
    @ApiModelProperty(value = "主体代码")
    private String uniquecode;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "项目名称")
    private String projectname;
    @ApiModelProperty(value = "涉及项目")
    private Long pj0101;
    @ApiModelProperty(value = "问题描述")
    private String description;
    @ApiModelProperty(value = "投诉状态")
    private String complaintstatus;
    @ApiModelProperty(value = "回复内容")
    private String reply;
    @ApiModelProperty(value = "回复人")
    private String replytor;
    @ApiModelProperty(value = "回复时间")
    private Date replydate;
}