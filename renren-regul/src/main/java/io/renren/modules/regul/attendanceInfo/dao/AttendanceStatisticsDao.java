package io.renren.modules.regul.attendanceInfo.dao;

import io.renren.modules.regul.attendanceInfo.dto.AttendanceQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 考勤统计DAO
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
@Mapper
public interface AttendanceStatisticsDao {

    /**
     * 调用考勤状态统计存储过程
     *
     * @param month      月份（YYYY-MM格式）
     * @param pj0101     项目ID（可选）
     * @param personType 人员类型（可选）
     * @return 考勤状态统计结果
     */
    List<Map<String, Object>> getAttendanceStatusStatistics(
            @Param("month") String month,
            @Param("pj0101") Long pj0101,
            @Param("personType") String personType
    );

    /**
     * 调用工时统计存储过程
     *
     * @param month      月份（YYYY-MM格式）
     * @param pj0101     项目ID（可选）
     * @param personType 人员类型（可选）
     * @return 工时统计结果
     */
    List<Map<String, Object>> getAttendanceWorkhoursStatistics(
            @Param("month") String month,
            @Param("pj0101") Long pj0101,
            @Param("personType") String personType
    );

    /**
     * 调用综合考勤统计存储过程
     * 存储过程会将数据插入到T_ATTENDANCE_STATISTICS表中，并通过params的"result"键返回游标结果
     *
     * @param params 存储过程参数，包含month、pj0101、userId，调用后游标结果会放入"result"键中，影响行数放入"affectedRows"键中
     */
    void getAttendanceComprehensiveStatistics(Map<String, Object> params);

    /**
     * 获取指定月份的天数
     *
     * @param month 月份（YYYY-MM格式）
     * @return 该月天数
     */
    Integer getDaysInMonth(@Param("month") String month);

    /**
     * 获取人员基本信息
     *
     * @param queryDTO 查询参数
     * @return 人员信息列表
     */
    List<Map<String, Object>> getPersonInfo(AttendanceQueryDTO queryDTO);

    /**
     * 验证项目是否存在
     *
     * @param pj0101 项目ID
     * @return 是否存在
     */
    Boolean checkProjectExists(@Param("pj0101") Long pj0101);

    /**
     * 获取考勤原始数据（用于验证和调试）
     *
     * @param month  月份
     * @param userId 人员ID
     * @param pj0101 项目ID
     * @return 原始考勤数据
     */
    List<Map<String, Object>> getAttendanceRawData(
            @Param("month") String month,
            @Param("userId") Long userId,
            @Param("pj0101") Long pj0101
    );

    /**
     * 获取月度考勤汇总统计
     *
     * @param month      月份
     * @param pj0101     项目ID
     * @param personType 人员类型
     * @return 汇总统计数据
     */
    Map<String, Object> getMonthlyAttendanceSummary(
            @Param("month") String month,
            @Param("pj0101") Long pj0101,
            @Param("personType") String personType
    );


}
