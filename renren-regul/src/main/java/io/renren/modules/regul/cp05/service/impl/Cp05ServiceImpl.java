package io.renren.modules.regul.cp05.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.ot01.dao.Ot01Dao;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.regul.cp05.dao.Cp05Dao;
import io.renren.modules.regul.cp05.dto.Cp05DTO;
import io.renren.modules.regul.cp05.entity.Cp05Entity;
import io.renren.modules.regul.cp05.service.Cp05Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 企业担保管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-15
 */
@Service
public class Cp05ServiceImpl extends CrudServiceImpl<Cp05Dao, Cp05Entity, Cp05DTO> implements Cp05Service {

    @Autowired
    private Ot01Dao ot01Dao;

    @Override
    public QueryWrapper<Cp05Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Cp05Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Cp05DTO> pageList(Map<String, Object> params) {
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId", deptId);
        IPage<Cp05Entity> page = getPage(params, "", false);
        List<Cp05DTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Cp05DTO.class);
    }

    @Override
    public Result getInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        Long cp0501 = Long.valueOf(params.get("cp0501").toString());
        Cp05DTO dto = baseDao.selectInfoById(cp0501);
        List<Ot01DTO> files = ot01Dao.getBusinessData(cp0501, "43");
        dto.setMarginFiles(files);
        return result.ok(dto);
    }

}