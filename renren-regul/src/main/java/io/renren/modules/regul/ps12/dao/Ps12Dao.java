package io.renren.modules.regul.ps12.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.regul.ps12.dto.Ps12DTO;
import io.renren.modules.regul.ps12.entity.Ps12Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目负责人员表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-22
 */
@Mapper
public interface Ps12Dao extends BaseDao<Ps12Entity> {

    List<Ps12DTO> getList(@Param("page") Page<Ps12DTO> page, @Param("params") Map<String, Object> params);

    List<DictDTO> getPs12List();

    Ps12DTO getByUserId(Long userId);
}