package io.renren.modules.regul.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 到岗率-项目列表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "到岗率-项目列表")
public class AttendanceDayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String day;

    @ApiModelProperty(value = "是否考勤")
    private String iskq;

}