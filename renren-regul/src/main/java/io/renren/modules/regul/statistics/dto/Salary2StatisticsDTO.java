package io.renren.modules.regul.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发放情况统计
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "发放情况统计")
public class Salary2StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主管部门")
    private String areacode;
    @ApiModelProperty(value = "季度")
    private String quarter;
    @ApiModelProperty(value = "发放工资项目数")
    private String projects;
    @ApiModelProperty(value = "发放工资总额")
    private String salarysum;
    @ApiModelProperty(value = "欠薪项目数")
    private String noprojects;
}