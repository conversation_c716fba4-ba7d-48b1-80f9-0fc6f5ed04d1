package io.renren.modules.regul.pj13.controller;

import cn.hutool.core.util.StrUtil;
import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.regul.pj13.dto.Pj13DTO;
import io.renren.modules.regul.pj13.dto.Ps02ExitPageDTO;
import io.renren.modules.regul.pj13.dto.Ps04ExitPageDTO;
import io.renren.modules.regul.pj13.dto.Tm01ExitPageDTO;
import io.renren.modules.regul.pj13.service.Pj13Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@RestController
@RequestMapping("regul/pj13")
@Api(tags="退场审核表")
public class Pj13Controller {
    @Autowired
    private Pj13Service pj13Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:pj13:page")
    public Result<PageData<Pj13DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pj13DTO> page = pj13Service.page(params);

        return new Result<PageData<Pj13DTO>>().ok(page);
    }

    @GetMapping("ps02ExitPage")
    @ApiOperation("工人退场审核分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:pj13:ps02ExitPage")
    public Result<PageData<Ps02ExitPageDTO>> ps02ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps02ExitPageDTO> page = pj13Service.ps02ExitPage(params);

        return new Result<PageData<Ps02ExitPageDTO>>().ok(page);
    }

    @GetMapping("ps04ExitPage")
    @ApiOperation("管理人员退场审核分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:pj13:ps04ExitPage")
    public Result<PageData<Ps04ExitPageDTO>> ps04ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps04ExitPageDTO> page = pj13Service.ps04ExitPage(params);

        return new Result<PageData<Ps04ExitPageDTO>>().ok(page);
    }

    @GetMapping("tm01ExitPage")
    @ApiOperation("班组退场审核分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:pj13:tm01ExitPage")
    public Result<PageData<Tm01ExitPageDTO>> tm01ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Tm01ExitPageDTO> page = pj13Service.tm01ExitPage(params);

        return new Result<PageData<Tm01ExitPageDTO>>().ok(page);
    }

    @PostMapping("audit")
    @ApiOperation("退场审核")
    @LogOperation("退场审核")
    @RequiresPermissions("regul:pj13:audit")
    public Result audit(@RequestBody Pj13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        if ("2".equals(dto.getAuditstatus()) && StrUtil.isEmpty(dto.getAuditreason())) {
            return new Result().error("审核不通过时审核原因不能为空");
        }
        pj13Service.audit(dto);

        return new Result();
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("regul:pj13:info")
    public Result<Pj13DTO> get(@PathVariable("id") Long id){
        Pj13DTO data = pj13Service.get(id);

        return new Result<Pj13DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("regul:pj13:save")
    public Result save(@RequestBody Pj13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj13Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("regul:pj13:update")
    public Result update(@RequestBody Pj13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj13Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("regul:pj13:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj13Service.delete(ids);

        return new Result();
    }


}