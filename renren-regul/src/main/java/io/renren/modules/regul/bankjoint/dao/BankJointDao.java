package io.renren.modules.regul.bankjoint.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.regul.bankjoint.dto.BankJointDTO;
import io.renren.modules.regul.bankjoint.entity.BankJointEntity;
import io.renren.modules.regul.cd01.dto.Cd01DTO;
import io.renren.modules.regul.cd01.entity.Cd01Entity;
import io.renren.modules.regul.cd03.dto.CreditDTO;
import io.renren.modules.regul.pj01.dto.Pj01DTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 诚信评价主体表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-14
 */
@Mapper
public interface BankJointDao extends BaseDao<BankJointEntity> {

    List<BankJointDTO> getList(Map<String, Object> params);

    List<Pj01DTO> pj01Page(Map<String, Object> params);

    List<DictDTO> getJointBanks();

}