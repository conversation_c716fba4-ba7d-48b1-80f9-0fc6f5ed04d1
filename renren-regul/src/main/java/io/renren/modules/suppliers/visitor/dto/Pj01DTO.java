package io.renren.modules.suppliers.visitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chris
 * @Date : 2022-11-18
 **/
@Data
public class Pj01DTO {

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "主管部门")
    private String virareacode;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
    @ApiModelProperty(value = "联系人")
    private String linkman;
    @ApiModelProperty(value = "联系电话")
    private String linkphone;
    @ApiModelProperty(value = "授权状态")
    private String accreditstatus;
    @ApiModelProperty(value = "设备情况")
    private String devices;
}
