<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.pj04.dao.Pj04Dao">
    <select id="getList" resultType="io.renren.modules.regul.pj04.dto.Pj04DTO">
        select t.pj0401,
               b.name,
               b.idcardnumber,
               b.cellphone,
                (select y.dict_label
                from sys_dict_type x, sys_dict_data y
                where x.id = y.dict_type_id
                and x.dict_type = 'WORKTYPECODE'
                and y.dict_value = a.worktypecode) as worktypecode,
                (select x.teamname from b_tm01 x where x.tm0101 = a.tm0101) as teamname,
               d.name as projectname,
               c.trainingname,
               c.trainingtypecode,
               c.trainingmode,
               c.trainingstartdate,
               t.ispass,
               t.score
          from B_PJ04 t, b_ps02 a, b_ps01 b, b_pj03 c, b_pj01 d
         where t.ps0201 = a.ps0201
           and a.ps0101 = b.ps0101
           and t.pj0301 = c.pj0301
           and c.pj0101 = d.pj0101
        <if test="projectname !=null and projectname !=''">
            and d.name like '%'||#{projectname}||'%'
        </if>
        <if test="name !=null and name !=''">
            and b.name like '%'||#{name}||'%'
        </if>
        <if test="trainingtypecode !=null and trainingtypecode !=''">
            and c.trainingtypecode =#{trainingtypecode}
        </if>
    </select>
</mapper>