<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.pj03.dao.Pj03Dao">
    <select id="getList" resultType="io.renren.modules.regul.pj03.dto.Pj03DTO">
        select t.name as projectname,
               t.prjstatus,
               t.linkman,
               t.linkphone,
               a.*
          from B_PJ01 t
          left join b_pj03 a
            on t.pj0101 = a.pj0101
          where 1 = 1
        <if test="projectname !=null and projectname !=''">
            and t.name like '%'||#{projectname}||'%'
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            and t.prjstatus = #{prjstatus}
        </if>
    </select>
    <select id="getInfo" resultType="io.renren.modules.regul.pj04.dto.Pj04DTO">
        select b.name,
               b.idcardnumber,
               b.cellphone,
               c.teamname,
               t.score,
               (select y.dict_label
                  from sys_dict_type x, sys_dict_data y
                 where x.id = y.dict_type_id
                   and x.dict_type = 'WORKTYPECODE'
                   and y.dict_value = a.worktypecode) as worktypecode
          from B_PJ04 t, b_ps02 a, b_ps01 b, b_tm01 c
         where t.ps0201 = a.ps0201
           and a.ps0101 = b.ps0101
           and a.tm0101 = c.tm0101
           and t.pj0301 = #{pj0301}
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.regul.pj03.dto.Pj03DTO">
        select t.name as projectname,
               t.prjstatus,
               t.linkman,
               t.linkphone,
               a.*
          from B_PJ01 t,b_pj03 a
            where t.pj0101 = a.pj0101
            and a.pj0301 = #{pj0301}
    </select>
    <select id="statistics1" resultType="io.renren.modules.regul.pj03.dto.Training1StatisticsDTO">
        select t.*,
           case when t.workers = '0' then
           '0%'
           else
            round((trainingworkers / workers)*100, 2)||'%'
           end trainingpercent
          from (select t.name as projectname,
                       t.prjstatus,
                       b.areacode as virareacode,
                       (select x.corpname
                          from b_cp01 x, b_cp02 y
                         where x.cp0101 = y.cp0101
                           and y.pj0101 = t.pj0101
                           and y.corptype = '9') as corpname,
                       (select count(1) from b_ps02 x where x.pj0101 = t.pj0101) as workers,
                       (select count(1)
                          from b_pj04 x, b_pj03 y
                         where x.pj0301 = y.pj0301
                           and y.pj0101 = t.pj0101) as trainingworkers
                  from B_PJ01 t, r_pj01_dept a,sys_dept b
                 where t.pj0101 = a.pj0101
                   and t.dept_id = b.id
                   and a.dept_id = #{deptId}
        <if test="projectname !=null and projectname !=''">
            and t.name like '%'||#{projectname}||'%'
        </if>
        <if test="virareacode !=null and virareacode !=''">
            and b.areacode = #{virareacode}
        </if>) t
    </select>
    <select id="statistics2" resultType="io.renren.modules.regul.pj03.dto.Training2StatisticsDTO">
        select t.*,
               round((trainingworkers / workers) * 100, 2) || '%' as trainingpercent
          from (select t.dict_label as worktypecode,
                       (select count(1)
                          from b_ps02 x
                         where x.worktypecode = t.dict_value) as workers,
                       (select count(1)
                          from b_ps02 x, b_pj04 y
                         where x.ps0201 = y.ps0201
                           and x.worktypecode = t.dict_value) as trainingworkers
                  from SYS_DICT_DATA t
                 where t.dict_type_id = '206'
            <if test="worktypecode !=null and worktypecode !=''">
                and t.dict_value = #{worktypecode}
            </if>) t
         order by t.workers desc
    </select>
</mapper>