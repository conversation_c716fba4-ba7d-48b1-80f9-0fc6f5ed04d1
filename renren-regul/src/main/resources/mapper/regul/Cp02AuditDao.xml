<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.cp02.dao.Cp02AuditDao">
    <select id="auditPageList" resultType="io.renren.modules.regul.cp02.dto.Cp02AuditDTO">
        select t.*,b.corpname,b.corpcode,c.name,d.areacode as virareacode
          from B_CP02_audit t, r_pj01_dept a,b_cp01 b,b_pj01 c,sys_dept d
         where t.pj0101 = a.pj0101
           and a.dept_id = #{deptId}
           and t.cp0101 = b.cp0101
           and t.pj0101 = c.pj0101
           and c.dept_id = d.id
        <if test="name != null and name != ''">
            and c.name like '%'||#{name}||'%'
        </if>
        <if test="corpname != null and corpname != ''">
            and b.corpname like '%'||#{corpname}||'%'
        </if>
        <if test="auditstatus != null and auditstatus != ''">
            and t.auditstatus = #{auditstatus}
        </if>
        <if test="corptype != null and corptype != ''">
            and t.corptype = #{corptype}
        </if>
        <if test="virareacode != null and virareacode != ''">
            and d.areacode = #{virareacode}
        </if>
         order by t.auditstatus, t.create_date
    </select>
</mapper>