<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.jg08.dao.Jg08Dao">

    <select id="getList" resultType="io.renren.modules.regul.jg08.dto.Jg08DTO">
        select a.name as projectname, t.*
          from B_JG08 t, b_pj01 a
         where t.pj0101 = a.pj0101
        <if test="projectname !=null and projectname !=''">
            and a.name like '%'||#{projectname}||'%'
        </if>
        <if test="receivetype !=null and receivetype !=''">
            and t.receivetype = #{receivetype}
        </if>
        <if test="complaintname !=null and complaintname !=''">
            and t.complaintname like '%'||#{complaintname}||'%'
        </if>
        <if test="title !=null and title !=''">
            and t.title like '%'||#{title}||'%'
        </if>
         order by t.create_date desc
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.regul.jg08.dto.Jg08DTO">
        select a.name as projectname, t.*
          from B_JG08 t, b_pj01 a
         where t.pj0101 = a.pj0101
         and t.jg0801 = #{jg0801}
    </select>
    <select id="getJg09PageList" resultType="io.renren.modules.regul.jg09.dto.Jg09DTO">
        select t.* from B_JG09 t where t.jg0801 = #{jg0801} order by t.create_date desc
    </select>
</mapper>