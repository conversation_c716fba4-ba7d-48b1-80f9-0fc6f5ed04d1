<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.projectMonthly.dao.MonthlyDao">
    <select id="getProjectList" resultType="io.renren.modules.regul.projectMonthly.dto.ProjectMonthly">
        SELECT t.pj0101,
               t.name                  AS projectName,
               NVL(t.wages_state, '0') AS wagesState,
               NVL(a1.fileCount, '0')  AS fileCount
        FROM b_pj01 t
                 LEFT JOIN (select a.pj0101, count(b.ot0101) as fileCount
                            from b_cg10 a,
                                 b_ot01 b
                            where a.cg0901 = 1945721221703127042
                              and a.file_year = #{yearMonth}
                              and a.cg1001 = b.busis<PERSON>no
                              and b.whether = '1'
                            group by a.pj0101) a1 ON t.pj0101 = a1.pj0101
        WHERE t.prjstatus = '3'
    </select>
</mapper>