<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.suppliers.visitor.dao.Kq02VisitorDao">
    <select id="attendancePage" resultType="io.renren.modules.suppliers.visitor.dto.Kq02VisitorDTO">
        select a.KQ0201,
                c.name,
                a.user_id,
                a.PJ0101,
                a.PERSON_NAME personname,
                a.PERSON_TYPE personType,
                a.DIRECTION,
                a.ATTENDTYPE,
                a.CHECKDATE checkDate,
                a.IMAGE_URL imageUrl
            from B_KQ02_VISITOR a, R_PJ01_DEPT b, b_pj01 c
            where a.PJ0101 = b.PJ0101
            and a.PJ0101 = c.PJ0101
            and b.DEPT_ID = #{deptId}
            and a.person_type = '3'
            <if test="personname != null and personname != ''">
                and a.PERSON_NAME like '%' || #{personname} || '%'
            </if>
            <if test="startTime != null and startTime != ''">
                and to_Char(a.checkDate, 'yyyy-MM-dd') >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and to_Char(a.checkDate, 'yyyy-MM-dd') &lt;= #{endTime}
            </if>
            <if test="name != null and name != ''">
                and c.NAME like '%' || #{name} || '%'
            </if>
            order by a.kq0201 desc
    </select>
</mapper>