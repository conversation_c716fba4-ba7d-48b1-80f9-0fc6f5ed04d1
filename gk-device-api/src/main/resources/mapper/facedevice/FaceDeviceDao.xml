<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.facedevice.dao.FaceDeviceDao">
    <!--查询设备是否有需要待处理的数据-->
    <select id="selectHeartBeat" resultType="io.renren.facedevice.dto.HeartBeatRes">
        select a.ID, a.TYPE
        from SUP_DEVICE_TASK a
        where a.DEVICE_KEY = #{deviceKey}
          and a.RESULT = '-1'
        order by a.CREATE_DATE
    </select>

    <select id="selectType" resultType="java.lang.String">
        select a.TYPE
        from SUP_DEVICE_TASK a
        where a.ID = #{id}
          and a.DEVICE_KEY = #{deviceKey}
    </select>

    <select id="selectDelPerson" resultType="io.renren.facedevice.dto.PersonDel">
        select b.USER_ID as userId, b.NAME, '1' as delType,
        case b.user_type
         when 1 then
          (select x.idcardnumber
             from b_ps01 x, b_ps02 y
            where x.ps0101 = y.ps0101
              and y.ps0201 = b.user_id)
         when 2 then
          (select x.idcardnumber
             from b_ps01 x, b_ps03 y, b_ps04 z
            where x.ps0101 = y.ps0101
              and y.ps0301 = z.ps0301
              and z.ps0401 = b.user_id)
       end as idcardnumber
        from SUP_DEVICE_TASK a,
             SUP_DEVICE_TASK_DETAIL b
        where a.ID = b.TASK_ID
          and a.DEVICE_KEY = #{deviceKey}
          and a.ID = #{id}
    </select>

    <select id="selectAddPerson" resultType="io.renren.facedevice.dto.PersonAdd">
        select b.USER_ID as userId, b.NAME, b.USER_TYPE as userType, b.PHOTO,
        case b.user_type
         when 1 then
          (select x.idcardnumber
             from b_ps01 x, b_ps02 y
            where x.ps0101 = y.ps0101
              and y.ps0201 = b.user_id)
         when 2 then
          (select x.idcardnumber
             from b_ps01 x, b_ps03 y, b_ps04 z
            where x.ps0101 = y.ps0101
              and y.ps0301 = z.ps0301
              and z.ps0401 = b.user_id)
       end as idcardnumber
        from SUP_DEVICE_TASK a,
             SUP_DEVICE_TASK_DETAIL b
        where a.ID = b.TASK_ID
          and a.DEVICE_KEY = #{deviceKey}
          and a.ID = #{id}
    </select>

    <update id="updateTask">
        update sup_device_task t
        set t.result=#{result},
            t.msg=#{msg},
            t.update_date=sysdate
        where t.id = #{id}
    </update>

    <select id="selectPersonById" resultType="io.renren.facedevice.dto.Person">
        select d.name, c.userType,c.tm0101
        from (select a.ps0201 as userId, '1' as userType, a.ps0101,a.tm0101 as tm0101
              from b_ps02 a where a.pj0101 = #{pj0101} and a.in_or_out = '1'
              union all
              select b.ps0401 as userId, '2' as userType, e.ps0101,null as tm0101
              from b_ps04 b,
                   b_ps03 e
              where b.ps0301 = e.ps0301
                and b.pj0101 = #{pj0101} and b.in_or_out = '1') c,
             b_ps01 d
        where c.ps0101 = d.ps0101
          and c.userId = #{userId}
          union all
        select x.name, '3' as userType, null as tm0101
          from b_ps10 x, b_ps10_pj01 y
         where x.ps1001 = y.ps1001
           and x.ps1001 = #{userId}
           and y.pj0101 = #{pj0101}
    </select>
    <select id="selectWorkerById" resultType="io.renren.facedevice.dto.PersonDel">
        select t.idcardnumber as user_id,t.WORKERNAME as name from Z_WORKER t where t.userid=#{userId} and rownum &lt; 2
    </select>
    <select id="selectWorkerByCard" resultType="io.renren.facedevice.dto.Worker">
        select t.ps0201,t.tm0101
          from b_ps02 t, b_ps01 a
         where t.ps0101 = a.ps0101
           and a.idcardnumber = #{idcardnumber}
           and t.pj0101 = #{pj0101}
           and t.in_or_out = '1'
    </select>
    <select id="selectManagerById" resultType="io.renren.facedevice.dto.PersonDel">
        select t.pmname as name,t.pmidcardnumber as user_id from Z_MANAGER t where t.userid=#{userId} and rownum &lt; 2
    </select>
    <select id="selectManagerIdByCard" resultType="java.lang.Long">
        select t.ps0401
          from b_ps04 t, b_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and b.idcardnumber = #{idcardnumber}
           and t.pj0101 = #{pj0101}
           and t.in_or_out = '1'
    </select>
    <select id="selectTransById" resultType="io.renren.facedevice.dto.PersonDel">
        select idcardnumber as user_id, name
          from (select t.pmidcardnumber as idcardnumber, t.pmname as name
                  from Z_MANAGER t
                 where t.userid = #{userId}
                union all
                select t.idcardnumber, t.workername as name
                  from Z_worker t
                 where t.userid = #{userId})
         where rownum &lt; 2
    </select>
</mapper>