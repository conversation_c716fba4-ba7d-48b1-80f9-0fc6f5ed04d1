

package io.renren.facedevice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员下发
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "人员下发")
public class PersonAdd implements Serializable {

    private static final long serialVersionUID = -4693585452647871354L;

    @ApiModelProperty(value = "用户工号")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "人员类型")
    private Integer userType;

    @ApiModelProperty(value = "人脸照片")
    private String photo;

    @ApiModelProperty(value = "身份证号")
    private String idcardnumber;
}